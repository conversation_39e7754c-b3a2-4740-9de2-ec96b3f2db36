{"version": 3, "file": "noOffset-CvDeqYoa.js", "sources": ["../../src/views/cash/debit/noOffset.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"Subject & Method\": \"Subject & Method\",\r\n    \"Business Date\": \"Business Date\",\r\n    \"Payment\": \"Payment\",\r\n    \"Details\": \"Details\",\r\n    \"Operator\": \"Operator\",\r\n    \"Actions\": \"Actions\",\r\n    \"Revocation\": \"Revocation\",\r\n    \"Print\": \"Print\",\r\n    \"Cash Bill Revocation\": \"Cash Bill Revocation\",\r\n    \"Remark\": \"Remark\",\r\n    \"Please enter a remark\": \"Please enter a remark\",\r\n    \"Confirm\": \"Confirm\",\r\n    \"Cancel\": \"Cancel\",\r\n    \"Close\": \"Close\",\r\n    \"Expense Subject\": \"Expense Subject\",\r\n    \"Detailed Information\": \"Detailed Information\",\r\n    \"Amount\": \"Amount\",\r\n    \"Revocation successful!\": \"Revocation successful!\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"Subject & Method\": \"科目&方式\",\r\n    \"Business Date\": \"营业日期\",\r\n    \"Payment\": \"付款\",\r\n    \"Details\": \"详情\",\r\n    \"Operator\": \"操作人\",\r\n    \"Actions\": \"操作\",\r\n    \"Revocation\": \"冲账\",\r\n    \"Print\": \"打印\",\r\n    \"Cash Bill Revocation\": \"现付账冲账\",\r\n    \"Remark\": \"备注\",\r\n    \"Please enter a remark\": \"请输入备注\",\r\n    \"Confirm\": \"确定\",\r\n    \"Cancel\": \"取消\",\r\n    \"Close\": \"关闭\",\r\n    \"Expense Subject\": \"消费科目\",\r\n    \"Detailed Information\": \"详细信息\",\r\n    \"Amount\": \"金额\",\r\n    \"Revocation successful!\": \"冲账成功！\"\r\n  },\r\n  \"km\": {\r\n    \"Subject & Method\": \"ប្រធានបទ និងវិធី\",\r\n    \"Business Date\": \"កាលបរិច្ឆេទ\",\r\n    \"Payment\": \"ទូទាត់\",\r\n    \"Details\": \"លម្អិត\",\r\n    \"Operator\": \"ប្រតិបត្តិករ\",\r\n    \"Actions\": \"សកម្មភាព\",\r\n    \"Revocation\": \"ដកហូត\",\r\n    \"Print\": \"បោះពុម្ព\",\r\n    \"Cash Bill Revocation\": \"ដកហូតវិក័យប័ត្រ\",\r\n    \"Remark\": \"ចំណាំ\",\r\n    \"Please enter a remark\": \"បញ្ចូលចំណាំ\",\r\n    \"Confirm\": \"បញ្ជាក់\",\r\n    \"Cancel\": \"បោះបង់\",\r\n    \"Close\": \"បិទ\",\r\n    \"Expense Subject\": \"ប្រធានបទចំណាយ\",\r\n    \"Detailed Information\": \"ព័ត៌មានលម្អិត\",\r\n    \"Amount\": \"ចំនួនទឹកប្រាក់\",\r\n    \"Revocation successful!\": \"ដកហូតជោគជ័យ!\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport { accRecordApi, cashBillOrderApi, printApi } from '@/api/modules/index'\r\nimport { BillType, PrintFormat } from '@/models/dict/constants'\r\nimport useUserStore from '@/store/modules/user'\r\nimport storage from '@/utils/storage'\r\nimport { createApp, h } from 'vue'\r\nimport A4BillPrint from './components/a4BillPrint.vue'\r\nimport PosBillPrint from './components/posBillPrint.vue'\r\n\r\ndefineOptions({\r\n  name: 'CashDebitList',\r\n})\r\n\r\nconst { t } = useI18n()\r\n\r\nconst userStore = useUserStore()\r\nconst { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()\r\n\r\nconst data = ref({\r\n  loading: false,\r\n  // Whether the table adapts to the height\r\n  tableAutoHeight: false,\r\n\r\n  formMode: 'dialog' as 'router' | 'dialog' | 'drawer',\r\n  // Details\r\n  formModeProps: {\r\n    visible: false,\r\n  },\r\n  // Search\r\n  search: {\r\n    dateStart: '',\r\n    dateEnd: '',\r\n    /** Account code */\r\n    accCode: '',\r\n    /** Whether settled 0: not settled 1: settled */\r\n    offset: '0',\r\n  },\r\n  searchFold: true,\r\n  // Batch operation\r\n  batch: {\r\n    enable: false,\r\n    selectionDataList: [],\r\n  },\r\n  // List data\r\n  dataList: [],\r\n})\r\n\r\nonMounted(() => {\r\n  getPrintLayout()\r\n})\r\n\r\nconst accCode = ref('')\r\nfunction open(code: string) {\r\n  accCode.value = code\r\n  getDataList()\r\n}\r\n\r\nfunction getDataList() {\r\n  data.value.loading = true\r\n  const params = {\r\n    ...getParams(),\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    accCode: accCode.value,\r\n    state: 0,\r\n    offset: data.value.search.offset,\r\n    ...(data.value.search.dateStart && {\r\n      dateStart: data.value.search.dateStart,\r\n    }),\r\n    ...(data.value.search.dateEnd && { dateEnd: data.value.search.dateEnd }),\r\n  }\r\n  cashBillOrderApi\r\n    .getCashBillOrderPage(params)\r\n    .then((res: any) => {\r\n      data.value.loading = false\r\n      if (res.code === 0 && !!res.data) {\r\n        data.value.dataList = res.data.list\r\n        pagination.value.total = res.data.total\r\n      }\r\n    })\r\n    .catch(() => {\r\n      data.value.loading = false\r\n    })\r\n}\r\n\r\nconst layout = ref('')\r\nasync function getPrintLayout() {\r\n  printApi\r\n    .getPrintLayout({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      billCode: BillType.BILL,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        layout.value = res.data.layout\r\n      }\r\n    })\r\n}\r\n\r\nfunction printView(row: any) {\r\n  accRecordApi\r\n    .getAccRecordList({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      cashBillOrderNo: row.cashBillOrderNo,\r\n    })\r\n    .then((res: any) => {\r\n      if (res.code === 0) {\r\n        const printData = res.data\r\n        // 创建一个隐藏的iframe用于打印\r\n        const printFrame = document.createElement('iframe')\r\n        printFrame.style.position = 'fixed'\r\n        printFrame.style.right = '0'\r\n        printFrame.style.bottom = '0'\r\n        printFrame.style.width = '0'\r\n        printFrame.style.height = '0'\r\n        printFrame.style.border = '0'\r\n\r\n        document.body.appendChild(printFrame)\r\n\r\n        // 检查contentWindow是否存在\r\n        const contentWindow = printFrame.contentWindow\r\n        if (!contentWindow) {\r\n          console.error('打印窗口创建失败')\r\n          return\r\n        }\r\n\r\n        const frameDoc = contentWindow.document\r\n\r\n        // 先打开文档进行写入\r\n        frameDoc.open()\r\n        frameDoc.write(`\r\n          <!DOCTYPE html>\r\n          <html>\r\n            <head>\r\n              <title>入账单打印</title>\r\n              <meta charset=\"UTF-8\">\r\n              <style>\r\n                body {\r\n                  margin: 0;\r\n                  padding: 0;\r\n                  font-family: SimSun, \"宋体\", sans-serif;\r\n                }\r\n                @media print {\r\n                  @page {\r\n                    size: 80mm auto;\r\n                    margin: 0;\r\n                  }\r\n                  body {\r\n                    margin: 5mm;\r\n                  }\r\n                }\r\n              </style>\r\n            </head>\r\n            <body>\r\n              <div id=\"app\"></div>\r\n            </body>\r\n          </html>\r\n        `)\r\n        frameDoc.close()\r\n\r\n        // 根据layout选择不同的打印组件\r\n        let printComponent = null\r\n        if (layout.value === PrintFormat.POS.toString()) {\r\n          printComponent = PosBillPrint\r\n        } else if (layout.value === PrintFormat.A4.toString()) {\r\n          printComponent = A4BillPrint\r\n        } else if (layout.value === PrintFormat.A412.toString()) {\r\n          printComponent = A4BillPrint\r\n        } else if (layout.value === PrintFormat.A413.toString()) {\r\n          printComponent = A4BillPrint\r\n        } else {\r\n          // 默认使用POS格式\r\n          printComponent = PosBillPrint\r\n        }\r\n\r\n        // 使用Vue的渲染函数创建组件实例\r\n        const app = createApp({\r\n          render() {\r\n            return h(printComponent as any, {\r\n              printData,\r\n              hotelName: storage.local.has('hname') ? storage.local.get('hname') : '',\r\n              operator: row.operator || '',\r\n              remark: row.remark || '',\r\n            })\r\n          },\r\n        })\r\n\r\n        // 等待iframe加载完成后再挂载Vue应用\r\n        setTimeout(() => {\r\n          try {\r\n            // 挂载Vue应用\r\n            const appElement = frameDoc.getElementById('app')\r\n            if (appElement) {\r\n              app.mount(appElement)\r\n            } else {\r\n              console.error('打印容器元素未找到')\r\n            }\r\n            // 给Vue应用一点时间渲染\r\n            setTimeout(() => {\r\n              try {\r\n                // 使用浏览器原生打印预览\r\n                contentWindow.print()\r\n\r\n                // 打印完成后移除iframe\r\n                setTimeout(() => {\r\n                  document.body.removeChild(printFrame)\r\n                  app.unmount()\r\n                }, 1000)\r\n              } catch (e) {\r\n                console.error('打印失败:', e)\r\n              }\r\n            }, 300)\r\n          } catch (e) {\r\n            console.error('挂载应用失败:', e)\r\n          }\r\n        }, 100)\r\n      }\r\n    })\r\n    .catch((error) => {\r\n      console.error('获取打印数据失败:', error)\r\n      ElMessage.error('获取打印数据失败')\r\n    })\r\n}\r\n\r\n// Page size change\r\nfunction sizeChange(size: number) {\r\n  onSizeChange(size).then(() => getDataList())\r\n}\r\n\r\n// Current page change (pagination)\r\nfunction currentChange(page = 1) {\r\n  onCurrentChange(page).then(() => getDataList())\r\n}\r\n\r\n// Field sorting\r\nfunction sortChange({ prop, order }: { prop: string; order: string }) {\r\n  onSortChange(prop, order).then(() => getDataList())\r\n}\r\n\r\nconst form = ref({\r\n  remark: '',\r\n})\r\nconst myVisible = ref(false)\r\nconst cashBillOrderNo = ref('')\r\n// 冲账操作\r\nfunction chongZhang(row) {\r\n  myVisible.value = true\r\n  cashBillOrderNo.value = row.cashBillOrderNo\r\n}\r\nfunction onCancel() {\r\n  form.value.remark = ''\r\n  myVisible.value = false\r\n}\r\n\r\nconst loading = ref(false)\r\nfunction save() {\r\n  loading.value = true\r\n  cashBillOrderApi\r\n    .revocationCashBillOrder({\r\n      gcode: userStore.gcode,\r\n      hcode: userStore.hcode,\r\n      cashBillOrderNo: cashBillOrderNo.value,\r\n      remark: form.value.remark,\r\n    })\r\n    .then((res: any) => {\r\n      loading.value = false\r\n      if (res.code === 0) {\r\n        ElMessage.success(t('Revocation successful!'))\r\n        onCancel()\r\n        getDataList()\r\n      }\r\n    })\r\n    .catch(() => {\r\n      loading.value = false\r\n    })\r\n}\r\n\r\ndefineExpose({ open })\r\n</script>\r\n\r\n<template>\r\n  <div :class=\"{ 'absolute-container': data.tableAutoHeight }\">\r\n    <page-main>\r\n      <el-table v-loading=\"data.loading\" class=\"list-table\" :data=\"data.dataList\" highlight-current-row stripe border height=\"100%\" @sort-change=\"sortChange\" @selection-change=\"data.batch.selectionDataList = $event\">\r\n        <el-table-column :label=\"t('Subject & Method')\" min-width=\"120\">\r\n          <template #default=\"scope\">\r\n            <span>{{ scope.row.subName }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"bizDate\" :label=\"t('Business Date')\" width=\"120\" />\r\n        <el-table-column :label=\"t('Payment')\" width=\"120\" align=\"right\">\r\n          <template #default=\"scope\">\r\n            <span class=\"amount\">￥{{ scope.row.payFee }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('Details')\" min-width=\"200\">\r\n          <template #default=\"scope\">\r\n            <span class=\"detail-text\">{{ scope.row.buyContent }}</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('Operator')\" width=\"180\" align=\"center\">\r\n          <template #default=\"scope\">\r\n            <div class=\"operator-info\">\r\n              <span class=\"operator-name\">{{ scope.row.operator }} - {{ scope.row.shiftName }}</span>\r\n              <span class=\"operator-time\">{{ scope.row.createTime }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('Actions')\" width=\"180\" align=\"center\" fixed=\"right\">\r\n          <template #default=\"scope\">\r\n            <div class=\"action-buttons\">\r\n              <el-link v-auth=\"'finance:cash-bill-order:update:revocation'\" type=\"primary\" @click=\"chongZhang(scope.row)\">\r\n                {{ t('Revocation') }}\r\n              </el-link>\r\n              <el-link type=\"primary\" @click=\"printView(scope.row)\">\r\n                {{ t('Print') }}\r\n              </el-link>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <el-pagination\r\n        v-if=\"pagination.total > 10\"\r\n        :current-page=\"pagination.pageNo\"\r\n        :total=\"pagination.total\"\r\n        :page-size=\"pagination.pageSize\"\r\n        :page-sizes=\"pagination.sizes\"\r\n        :layout=\"pagination.layout\"\r\n        :hide-on-single-page=\"false\"\r\n        class=\"pagination\"\r\n        background\r\n        @size-change=\"sizeChange\"\r\n        @current-change=\"currentChange\"\r\n      />\r\n      <el-dialog v-model=\"myVisible\" width=\"600px\" :title=\"t('Cash Bill Revocation')\" :close-on-click-modal=\"false\" append-to-body destroy-on-close :show-close=\"true\" @close=\"onCancel()\">\r\n        <div>\r\n          <el-form ref=\"formRef\" :model=\"form\" size=\"default\" label-width=\"90px\" label-suffix=\":\">\r\n            <el-form-item :label=\"t('Remark')\">\r\n              <el-input v-model=\"form.remark\" type=\"textarea\" :rows=\"3\" :placeholder=\"t('Please enter a remark')\" />\r\n            </el-form-item>\r\n          </el-form>\r\n          <div class=\"dialog-footer\">\r\n            <el-button @click=\"onCancel\">\r\n              {{ t('Cancel') }}\r\n            </el-button>\r\n            <el-button :loading=\"loading\" type=\"primary\" @click=\"save\">\r\n              {{ t('Confirm') }}\r\n            </el-button>\r\n          </div>\r\n        </div>\r\n      </el-dialog>\r\n    </page-main>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.dialog-footer {\r\n  display: flex;\r\n  justify-content: flex-end;\r\n  gap: 8px;\r\n  margin-top: 20px;\r\n}\r\n\r\n.list-table {\r\n  :deep(.el-table__header) {\r\n    th {\r\n      background-color: #f5f7fa;\r\n      color: #606266;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n\r\n  .amount {\r\n    font-weight: 500;\r\n    color: #f56c6c;\r\n  }\r\n\r\n  .detail-text {\r\n    color: #606266;\r\n    display: -webkit-box;\r\n    -webkit-line-clamp: 2;\r\n    -webkit-box-orient: vertical;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .operator-info {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 4px;\r\n\r\n    .operator-name {\r\n      color: #606266;\r\n    }\r\n\r\n    .operator-time {\r\n      font-size: 13px;\r\n      color: #909399;\r\n    }\r\n  }\r\n\r\n  .action-buttons {\r\n    display: flex;\r\n    justify-content: center;\r\n    gap: 8px;\r\n\r\n    .el-link {\r\n      margin: 0;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "names": ["t", "useI18n", "userStore", "useUserStore", "pagination", "getParams", "onSizeChange", "onCurrentChange", "onSortChange", "usePagination", "data", "ref", "loading", "tableAutoHeight", "formMode", "formModeProps", "visible", "search", "dateStart", "dateEnd", "accCode", "offset", "searchFold", "batch", "enable", "selectionDataList", "dataList", "onMounted", "async", "printApi", "getPrintLayout", "gcode", "hcode", "billCode", "BillType", "BILL", "then", "res", "code", "layout", "value", "getDataList", "params", "state", "cashBillOrderApi", "getCashBillOrderPage", "list", "total", "catch", "sizeChange", "size", "currentChange", "page", "sortChange", "prop", "order", "form", "remark", "myVisible", "cashBillOrderNo", "onCancel", "save", "revocationCashBillOrder", "ElMessage", "success", "__expose", "open", "row", "accRecordApi", "getAccRecordList", "printData", "printFrame", "document", "createElement", "style", "position", "right", "bottom", "width", "height", "border", "body", "append<PERSON><PERSON><PERSON>", "contentWindow", "console", "error", "frameDoc", "write", "close", "printComponent", "PosBillPrint", "PrintFormat", "POS", "toString", "A4", "A412", "A413", "A4BillPrint", "app", "createApp", "render", "h", "hotelName", "storage", "local", "has", "get", "operator", "setTimeout", "appElement", "getElementById", "mount", "print", "<PERSON><PERSON><PERSON><PERSON>", "unmount", "e"], "mappings": "g+CA8EM,MAAAA,EAAEA,GAAMC,IAERC,EAAYC,KACZC,WAAEA,EAAYC,UAAAA,EAAAC,aAAWA,mBAAcC,GAAiBC,aAAAA,IAAiBC,IAEzEC,GAAOC,EAAI,CACfC,SAAS,EAETC,iBAAiB,EAEjBC,SAAU,SAEVC,cAAe,CACbC,SAAS,GAGXC,OAAQ,CACNC,UAAW,GACXC,QAAS,GAETC,QAAS,GAETC,OAAQ,KAEVC,YAAY,EAEZC,MAAO,CACLC,QAAQ,EACRC,kBAAmB,IAGrBC,SAAU,KAGZC,GAAU,MAuCVC,iBACEC,EACGC,eAAe,CACdC,MAAO7B,EAAU6B,MACjBC,MAAO9B,EAAU8B,MACjBC,SAAUC,EAASC,OAEpBC,MAAMC,IACY,IAAbA,EAAIC,OACCC,GAAAC,MAAQH,EAAI3B,KAAK6B,OAAA,GAE3B,CAjDYT,EAAA,IAGX,MAAAV,GAAUT,EAAI,IAMpB,SAAS8B,KACP/B,GAAK8B,MAAM5B,SAAU,EACrB,MAAM8B,EAAS,IACVrC,IACH0B,MAAO7B,EAAU6B,MACjBC,MAAO9B,EAAU8B,MACjBZ,QAASA,GAAQoB,MACjBG,MAAO,EACPtB,OAAQX,GAAK8B,MAAMvB,OAAOI,UACtBX,GAAK8B,MAAMvB,OAAOC,WAAa,CACjCA,UAAWR,GAAK8B,MAAMvB,OAAOC,cAE3BR,GAAK8B,MAAMvB,OAAOE,SAAW,CAAEA,QAAST,GAAK8B,MAAMvB,OAAOE,UAEhEyB,EACGC,qBAAqBH,GACrBN,MAAMC,IACL3B,GAAK8B,MAAM5B,SAAU,EACJ,IAAbyB,EAAIC,MAAgBD,EAAI3B,OACrBA,GAAA8B,MAAMd,SAAWW,EAAI3B,KAAKoC,KACpB1C,EAAAoC,MAAMO,MAAQV,EAAI3B,KAAKqC,MAAA,IAGrCC,OAAM,KACLtC,GAAK8B,MAAM5B,SAAU,CAAA,GACtB,CAGC,MAAA2B,GAAS5B,EAAI,IA8InB,SAASsC,GAAWC,GAClB5C,GAAa4C,GAAMd,MAAK,IAAMK,MAAa,CAIpC,SAAAU,GAAcC,EAAO,GAC5B7C,GAAgB6C,GAAMhB,MAAK,IAAMK,MAAa,CAIhD,SAASY,IAAWC,KAAEA,EAAMC,MAAAA,IAC1B/C,GAAa8C,EAAMC,GAAOnB,MAAK,IAAMK,MAAa,CAGpD,MAAMe,GAAO7C,EAAI,CACf8C,OAAQ,KAEJC,GAAY/C,GAAI,GAChBgD,GAAkBhD,EAAI,IAM5B,SAASiD,KACPJ,GAAKhB,MAAMiB,OAAS,GACpBC,GAAUlB,OAAQ,CAAA,CAGd,MAAA5B,GAAUD,GAAI,GACpB,SAASkD,KACPjD,GAAQ4B,OAAQ,EAChBI,EACGkB,wBAAwB,CACvB/B,MAAO7B,EAAU6B,MACjBC,MAAO9B,EAAU8B,MACjB2B,gBAAiBA,GAAgBnB,MACjCiB,OAAQD,GAAKhB,MAAMiB,SAEpBrB,MAAMC,IACLzB,GAAQ4B,OAAQ,EACC,IAAbH,EAAIC,OACIyB,EAAAC,QAAQhE,EAAE,2BACX4D,KACGnB,KAAA,IAGfO,OAAM,KACLpC,GAAQ4B,OAAQ,CAAA,GACjB,QAGQyB,EAAA,CAAEC,KAnOf,SAAc5B,GACZlB,GAAQoB,MAAQF,EACJG,IAAA,0pCAiMM0B,QAClBT,GAAUlB,OAAQ,OAClBmB,GAAgBnB,MAAQ2B,EAAIR,iBAF9B,IAAoBQ,sKAnJDA,aACjBC,EACGC,iBAAiB,CAChBtC,MAAO7B,EAAU6B,MACjBC,MAAO9B,EAAU8B,MACjB2B,gBAAiBQ,EAAIR,kBAEtBvB,MAAMC,IACD,GAAa,IAAbA,EAAIC,KAAY,CAClB,MAAMgC,EAAYjC,EAAI3B,KAEhB6D,EAAaC,SAASC,cAAc,UAC1CF,EAAWG,MAAMC,SAAW,QAC5BJ,EAAWG,MAAME,MAAQ,IACzBL,EAAWG,MAAMG,OAAS,IAC1BN,EAAWG,MAAMI,MAAQ,IACzBP,EAAWG,MAAMK,OAAS,IAC1BR,EAAWG,MAAMM,OAAS,IAEjBR,SAAAS,KAAKC,YAAYX,GAG1B,MAAMY,EAAgBZ,EAAWY,cACjC,IAAKA,EAEH,YADAC,QAAQC,MAAM,YAIhB,MAAMC,EAAWH,EAAcX,SAG/Bc,EAASpB,OACToB,EAASC,MAAM,ytBA4BfD,EAASE,QAGT,IAAIC,EAAiB,KAEFC,EADfnD,GAAOC,QAAUmD,EAAYC,IAAIC,WAClBH,EACRnD,GAAOC,QAAUmD,EAAYG,GAAGD,YAEhCtD,GAAOC,QAAUmD,EAAYI,KAAKF,YAElCtD,GAAOC,QAAUmD,EAAYK,KAAKH,WAH1BI,EAOAP,EAInB,MAAMQ,EAAMC,EAAU,CACpBC,OAAS,IACAC,EAAEZ,EAAuB,CAC9BnB,YACAgC,UAAWC,EAAQC,MAAMC,IAAI,SAAWF,EAAQC,MAAME,IAAI,SAAW,GACrEC,SAAUxC,EAAIwC,UAAY,GAC1BlD,OAAQU,EAAIV,QAAU,OAM5BmD,YAAW,KACL,IAEI,MAAAC,EAAavB,EAASwB,eAAe,OACvCD,EACFX,EAAIa,MAAMF,GAEVzB,QAAQC,MAAM,aAGhBuB,YAAW,KACL,IAEFzB,EAAc6B,QAGdJ,YAAW,KACApC,SAAAS,KAAKgC,YAAY1C,GAC1B2B,EAAIgB,SAAQ,GACX,WACIC,GACC/B,QAAAC,MAAM,QAAS8B,EAAC,IAEzB,WACIA,GACC/B,QAAAC,MAAM,UAAW8B,EAAC,IAE3B,IAAG,KAGTnE,OAAOqC,IACED,QAAAC,MAAM,YAAaA,GAC3BtB,EAAUsB,MAAM,WAAU,IA1HhC,IAAmBlB"}