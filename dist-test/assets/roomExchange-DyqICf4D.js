import{d as e,aj as o,ai as a,b as t,B as l,y as r,o as s,c as d,f as u,w as i,u as m,i as n,h as c,Y as p,e as g,g as b,F as f,ag as v,R,a6 as h,aR as y,av as N,q as C,m as k,b0 as _,b1 as j,aS as x,b2 as U,n as T,k as V,x as S,ay as w,j as B}from"./index-CkEhI1Zk.js";/* empty css                  *//* empty css                   *//* empty css                *//* empty css               *//* empty css                  *//* empty css                     *//* empty css                  *//* empty css                    *//* empty css               *//* empty css                 *//* empty css               *//* empty css                */import"./el-form-item-l0sNRNKZ.js";/* empty css                  */import{g as Y}from"./generalConfig.api-CEBBd8kx.js";import{o as E}from"./order.api-B-JCVvq6.js";import{z as F,O as q,B as z,E as D}from"./constants-Cg3j_uH4.js";import H from"./arrangeRooms-DLQ6Ij2m.js";import{_ as M}from"./_plugin-vue_export-helper-BCo6x5W8.js";/* empty css                          *//* empty css                       *//* empty css                        */import"./book.api-ERXvEXQF.js";import"./dictData.api-DUabpYqy.js";const O={class:"card-header"},P={class:"card-header"},A=e({__name:"roomExchange",props:{modelValue:{type:Boolean},orderNo:{default:""},rNo:{default:""},rCode:{default:""},rtName:{default:""},vipPrice:{default:0},planCheckinTime:{default:""},planCheckoutTime:{default:""},checkinType:{default:""},hourCode:{default:""},guestSrcTypeName:{default:""},guestSrcType:{default:""},guestCode:{default:""},channelCode:{default:""},orderSource:{default:""}},emits:["update:modelValue","success"],setup(e,{emit:M}){const A=e,G=M,{t:I}=o(),K=a(),J=t(),L=l({get:()=>A.modelValue,set(e){G("update:modelValue",e)}}),Q=t({gcode:K.gcode,hcode:K.hcode,orderNo:A.orderNo,isFreeUpgrade:"0",targetRoom:{rNo:"",rCode:"",rtName:"",rtCode:"",isBookedRoom:""},reason:{code:"",name:"",value:""}}),W=t(!1),X=t(1),Z=t(),$=t([]),ee=t({code:[{required:!0,message:"请选择换房原因",trigger:"blur"}],value:[{required:!0,message:"请输入升级原因",trigger:"blur"}],rNo:[{required:!0,message:"请选择要换的房间",trigger:"blur"}],vipPrice:[{required:!0,message:"请输入房价",trigger:"blur"}]});function oe(){if(""===Q.value.targetRoom.rNo)y({message:I("selectRoom"),type:"error",center:!1});else{const e={isAutoTask:Z.value,gcode:K.gcode,hcode:K.hcode,orderNo:A.orderNo,sourceRcode:A.rCode,targetRcode:Q.value.targetRoom.rCode,remark:Q.value.reason.value,isFree:Q.value.isFreeUpgrade,changeReason:Q.value.reason.code};E.changeRoom(e).then((e=>{0===e.code?(y.success({message:I("roomChangeSuccess",{rNo:A.rNo}),type:"success",center:!0}),G("success"),ae()):y.error(e.msg)}))}}function ae(){L.value=!1}r((()=>{Y.list({gcode:K.gcode,isG:z.YES,type:D,isEnable:z.YES}).then((e=>{$.value=e.data})),async function(){const e={gcode:K.gcode,types:[F].join(",")},{data:o}=await Y.list(e);o.forEach((e=>{e.code==q.CHECK_OUT&&(Z.value=e.value)}))}()}));const te=t({visible:!1,bookNo:"",batchNo:"",orderNo:"",rtCode:"",rtName:"",rtState:"",rNos:[],planCheckinTime:"",planCheckoutTime:"",isBookedRoom:"",checkinType:"",hourCode:"",guestSrcTypeName:"",guestSrcType:"",guestCode:"",channelCode:"",orderSource:""});function le(){te.value.rNos=[],te.value.bookNo="",te.value.batchNo="",te.value.orderNo="",te.value.rtCode=Q.value.targetRoom.rtCode?Q.value.targetRoom.rtCode:"",te.value.rtName=Q.value.targetRoom.rtName?Q.value.targetRoom.rtName:"",te.value.isBookedRoom=Q.value.targetRoom.isBookedRoom?Q.value.targetRoom.isBookedRoom:"",Q.value.targetRoom.rNo&&te.value.rNos.push(Q.value.targetRoom.rNo),te.value.planCheckinTime=N(A.planCheckinTime).format("YYYY-MM-DD HH:mm"),te.value.planCheckoutTime=N(A.planCheckoutTime).format("YYYY-MM-DD HH:mm"),te.value.checkinType=A.checkinType,te.value.hourCode=A.hourCode,te.value.guestSrcTypeName=A.guestSrcTypeName,te.value.guestSrcType=A.guestSrcType,te.value.guestCode=A.guestCode,te.value.channelCode=A.channelCode,te.value.orderSource=A.orderSource,te.value.visible=!0}function re(e){e&&(Q.value.targetRoom={rNo:e.rNo,rCode:e.rCode,rtName:e.rtName,rtCode:e.rtCode,isBookedRoom:e.isBookedRoom})}return(e,o)=>{const a=C,t=k,l=_,r=j,y=x,N=U,Y=T,E=B,F=V,q=S,z=w;return s(),d("div",null,[u(z,{modelValue:m(L),"onUpdate:modelValue":o[6]||(o[6]=e=>n(L)?L.value=e:null),title:m(I)("changeRoom"),width:"600px","close-on-click-modal":!1,"append-to-body":"","destroy-on-close":""},{footer:i((()=>[u(Y,{modelValue:m(Z),"onUpdate:modelValue":o[5]||(o[5]=e=>n(Z)?Z.value=e:null),"true-value":"1","false-value":"0",label:"下发客房清扫任务"},null,8,["modelValue"]),u(a,{size:"large",class:"ml-20px",onClick:ae},{default:i((()=>[c(p(m(I)("cancel")),1)])),_:1}),u(a,{type:"primary",size:"large",onClick:oe},{default:i((()=>[c(p(m(I)("submit")),1)])),_:1})])),default:i((()=>[1===m(X)?(s(),g(q,{key:0,ref_key:"formRef",ref:J,model:m(Q).targetRoom,rules:m(ee),"label-width":"127px","label-suffix":"："},{default:i((()=>[u(N,{style:{"margin-top":"10px"}},{default:i((()=>[u(r,{span:11},{default:i((()=>[u(l,{class:"box-card",shadow:"never"},{header:i((()=>[b("div",O,[b("span",null,p(m(I)("originalRoom")),1),u(a,{class:"button",text:""})])])),default:i((()=>[u(t,{label:m(I)("roomNumber"),"label-width":"65px",style:{"margin-top":"-12px"}},{default:i((()=>[c(p(A.rNo),1)])),_:1},8,["label"]),u(t,{label:m(I)("roomType"),"label-width":"65px",style:{"margin-top":"-12px"}},{default:i((()=>[c(p(A.rtName),1)])),_:1},8,["label"])])),_:1})])),_:1}),u(r,{span:2,style:{display:"flex","align-items":"center","justify-content":"center"}},{default:i((()=>o[8]||(o[8]=[b("i",{class:"i-ep:right",style:{"font-size":"xx-large"}},null,-1)]))),_:1}),u(r,{span:11},{default:i((()=>[u(l,{class:"box-card",shadow:"never"},{header:i((()=>[b("div",P,[b("span",null,p(m(I)("newRoom")),1),u(a,{type:"primary",plain:"",onClick:le},{default:i((()=>[c(p(m(I)("selectRoom")),1)])),_:1})])])),default:i((()=>[u(t,{label:m(I)("roomNumber"),"label-width":"70px",style:{"margin-top":"-12px"}},{default:i((()=>[u(y,{modelValue:m(Q).targetRoom.rNo,"onUpdate:modelValue":o[0]||(o[0]=e=>m(Q).targetRoom.rNo=e),disabled:""},null,8,["modelValue"])])),_:1},8,["label"]),u(t,{label:m(I)("roomType"),"label-width":"70px",style:{"margin-top":"-12px"}},{default:i((()=>[u(y,{modelValue:m(Q).targetRoom.rtName,"onUpdate:modelValue":o[1]||(o[1]=e=>m(Q).targetRoom.rtName=e),disabled:""},null,8,["modelValue"])])),_:1},8,["label"])])),_:1})])),_:1})])),_:1}),u(N,{style:{"margin-top":"10px"}},{default:i((()=>[u(r,{span:24},{default:i((()=>[u(t,{label:m(I)("freeUpgrade")},{default:i((()=>[u(Y,{modelValue:m(Q).isFreeUpgrade,"onUpdate:modelValue":o[2]||(o[2]=e=>m(Q).isFreeUpgrade=e),label:m(I)("freeUpgrade"),"true-value":"1","false-value":"0"},null,8,["modelValue","label"])])),_:1},8,["label"])])),_:1})])),_:1}),u(N,{gutter:20},{default:i((()=>[u(r,{span:24},{default:i((()=>[m(W)?(s(),g(t,{key:1,label:m(I)("upgradeReason")},{default:i((()=>[u(y,{modelValue:m(Q).reason.value,"onUpdate:modelValue":o[4]||(o[4]=e=>m(Q).reason.value=e),type:"textarea",placeholder:m(I)("enterUpgradeReason"),prop:"value"},null,8,["modelValue","placeholder"])])),_:1},8,["label"])):(s(),g(t,{key:0,label:m(I)("selectUpgradeReason")},{default:i((()=>[u(F,{modelValue:m(Q).reason.code,"onUpdate:modelValue":o[3]||(o[3]=e=>m(Q).reason.code=e),placeholder:m(I)("pleaseSelectReason"),prop:"code",style:{width:"100%"}},{default:i((()=>[(s(!0),d(f,null,v(m($),(e=>(s(),g(E,{key:e.code,label:e.name,value:e.code},null,8,["label","value"])))),128))])),_:1},8,["modelValue","placeholder"])])),_:1},8,["label"]))])),_:1})])),_:1})])),_:1},8,["model","rules"])):R("",!0)])),_:1},8,["modelValue","title"]),m(te).visible?(s(),g(H,h({key:0,modelValue:m(te).visible,"onUpdate:modelValue":o[7]||(o[7]=e=>m(te).visible=e)},m(te),{onSelected:re}),null,16,["modelValue"])):R("",!0)])}}});function G(e){const o=e;o.__i18n=o.__i18n||[],o.__i18n.push({locale:"",resource:{en:{changeRoom:{t:0,b:{t:2,i:[{t:3}],s:"Change Room"}},originalRoom:{t:0,b:{t:2,i:[{t:3}],s:"Original Room"}},roomNumber:{t:0,b:{t:2,i:[{t:3}],s:"Number"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"Type"}},newRoom:{t:0,b:{t:2,i:[{t:3}],s:"New Room"}},selectRoom:{t:0,b:{t:2,i:[{t:3}],s:"Select Room"}},freeUpgrade:{t:0,b:{t:2,i:[{t:3}],s:"Free Upgrade"}},selectUpgradeReason:{t:0,b:{t:2,i:[{t:3}],s:"Upgrade Reason"}},upgradeReason:{t:0,b:{t:2,i:[{t:3}],s:"Upgrade Reason"}},pleaseSelectReason:{t:0,b:{t:2,i:[{t:3}],s:"Please select a reason for room change"}},enterUpgradeReason:{t:0,b:{t:2,i:[{t:3}],s:"Please enter the free upgrade reason"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"Cancel"}},submit:{t:0,b:{t:2,i:[{t:3}],s:"Submit"}},roomChangeSuccess:{t:0,b:{t:2,i:[{t:3,v:'"'},{t:4,k:"rNo"},{t:3,v:'" room change successful'}]}}},"zh-cn":{changeRoom:{t:0,b:{t:2,i:[{t:3}],s:"换房"}},originalRoom:{t:0,b:{t:2,i:[{t:3}],s:"原房间"}},roomNumber:{t:0,b:{t:2,i:[{t:3}],s:"房号"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"房型"}},newRoom:{t:0,b:{t:2,i:[{t:3}],s:"新房间"}},selectRoom:{t:0,b:{t:2,i:[{t:3}],s:"选房"}},freeUpgrade:{t:0,b:{t:2,i:[{t:3}],s:"免费升级"}},selectUpgradeReason:{t:0,b:{t:2,i:[{t:3}],s:"请选择换房原因"}},upgradeReason:{t:0,b:{t:2,i:[{t:3}],s:"升级原因"}},pleaseSelectReason:{t:0,b:{t:2,i:[{t:3}],s:"请选择换房原因"}},enterUpgradeReason:{t:0,b:{t:2,i:[{t:3}],s:"请输入免费升级原因"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"取消"}},submit:{t:0,b:{t:2,i:[{t:3}],s:"提交"}},roomChangeSuccess:{t:0,b:{t:2,i:[{t:3,v:"「"},{t:4,k:"rNo"},{t:3,v:"」换房成功"}]}}},km:{changeRoom:{t:0,b:{t:2,i:[{t:3}],s:"ប្តូរបន្ទប់"}},originalRoom:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់ដើម"}},roomNumber:{t:0,b:{t:2,i:[{t:3}],s:"លេខបន្ទប់"}},roomType:{t:0,b:{t:2,i:[{t:3}],s:"ប្រភេទបន្ទប់"}},newRoom:{t:0,b:{t:2,i:[{t:3}],s:"បន្ទប់ថ្មី"}},selectRoom:{t:0,b:{t:2,i:[{t:3}],s:"ជ្រើសរើសបន្ទប់"}},freeUpgrade:{t:0,b:{t:2,i:[{t:3}],s:"ធ្វើឱ្យប្រសើរឡើងដោយឥតគិតថ្លៃ"}},selectUpgradeReason:{t:0,b:{t:2,i:[{t:3}],s:"ហេតុផលធ្វើឱ្យប្រសើរឡើង"}},upgradeReason:{t:0,b:{t:2,i:[{t:3}],s:"ហេតុផលធ្វើឱ្យប្រសើរឡើង"}},pleaseSelectReason:{t:0,b:{t:2,i:[{t:3}],s:"សូមជ្រើសរើសហេតុផលសម្រាប់ការផ្លាស់ប្តូរបន្ទប់"}},enterUpgradeReason:{t:0,b:{t:2,i:[{t:3}],s:"សូមបញ្ចូលហេតុផលធ្វើឱ្យប្រសើរឡើងដោយឥតគិតថ្លៃ"}},cancel:{t:0,b:{t:2,i:[{t:3}],s:"បោះបង់"}},submit:{t:0,b:{t:2,i:[{t:3}],s:"ដាក់ស្នើ"}},roomChangeSuccess:{t:0,b:{t:2,i:[{t:3,v:'បានផ្លាស់ប្តូរបន្ទប់ "'},{t:4,k:"rNo"},{t:3,v:'" ដោយជោគជ័យ'}]}}}}})}G(A);const I=M(A,[["__scopeId","data-v-b265b358"]]);export{I as default};
//# sourceMappingURL=roomExchange-DyqICf4D.js.map
