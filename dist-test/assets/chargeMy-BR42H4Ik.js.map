{"version": 3, "file": "chargeMy-BR42H4Ik.js", "sources": ["../../src/views/customer/member/info/components/FormMode/chargeMy.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"addRechargeActivity\": \"Add Recharge Activity\",\r\n    \"memberRecharge\": \"Member Recharge\",\r\n    \"editRechargeActivity\": \"Edit Recharge Activity\",\r\n    \"rechargeActivityDetails\": \"Recharge Activity Details\",\r\n    \"cancel\": \"Cancel\",\r\n    \"save\": \"Save\",\r\n    \"unknownError\": \"Unknown error\",\r\n    \"networkError\": \"Network error\",\r\n    \"basicInfo\":\"Basic Info\"\r\n  },\r\n  \"zh-cn\": {\r\n    \"addRechargeActivity\": \"新增充值活动\",\r\n    \"memberRecharge\": \"会员充值\",\r\n    \"editRechargeActivity\": \"修改充值活动\",\r\n    \"rechargeActivityDetails\": \"充值活动\",\r\n    \"cancel\": \"取消\",\r\n    \"save\": \"保存\",\r\n    \"unknownError\": \"未知错误\",\r\n    \"networkError\": \"网络错误\",\r\n    \"basicInfo\":\"基础信息\"\r\n  },\r\n  \"km\": {\r\n    \"addRechargeActivity\": \"បន្ថែមសកម្មភាពបញ្ចូលប្រាក់\",\r\n    \"memberRecharge\": \"ការបញ្ចូលប្រាក់សមាជិក\",\r\n    \"editRechargeActivity\": \"កែសម្រួលសកម្មភាពបញ្ចូលប្រាក់\",\r\n    \"rechargeActivityDetails\": \"ព័ត៌មានលម្អិតសកម្មភាពបញ្ចូលប្រាក់\",\r\n    \"cancel\": \"បោះបង់\",\r\n    \"save\": \"រក្សាទុក\",\r\n    \"unknownError\": \"កំហុសមិនស្គាល់\",\r\n    \"networkError\": \"កំហុសបណ្តាញ\",\r\n    \"basicInfo\":\"ព័ត៌មានមូលដ្ឋាន\"\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { FormInstance } from 'element-plus'\r\nimport type { DetailFormProps } from '../../types'\r\n\r\nimport Recharge from '../DetailForm/recharge.vue'\r\n\r\nconst props = withDefaults(\r\n  defineProps<\r\n    {\r\n      modelValue?: boolean\r\n    } & DetailFormProps\r\n  >(),\r\n  {\r\n    modelValue: false,\r\n    cardType: '1',\r\n  }\r\n)\r\n\r\nconst emits = defineEmits<{\r\n  'update:modelValue': [value: boolean]\r\n  editStatus: [value: boolean]\r\n  success: []\r\n}>()\r\n\r\nconst formRef = ref<FormInstance>()\r\n\r\nconst { t } = useI18n()\r\n\r\nconst myVisible = computed({\r\n  get() {\r\n    return props.modelValue\r\n  },\r\n  set(val) {\r\n    emits('update:modelValue', val)\r\n  },\r\n})\r\n\r\nconst doEdit = computed({\r\n  get() {\r\n    return props.isEdit\r\n  },\r\n  set(val) {\r\n    emits('editStatus', val)\r\n  },\r\n})\r\n\r\nconst title = computed(() => {\r\n  if (props.handle === 'create') {\r\n    return t('memberRecharge')\r\n  } else if (props.handle === 'edit') {\r\n    return t('editRechargeActivity')\r\n  } else {\r\n    return t('rechargeActivityDetails')\r\n  }\r\n})\r\n\r\nfunction onSubmit() {\r\n  // submit() 为组件内部方法\r\n  formRef.value\r\n    .submit()\r\n    .then(() => {\r\n      emits('success')\r\n      onCancel('')\r\n    })\r\n    .catch((error: any) => {\r\n      ElMessage.error({\r\n        message: error.message || t('unknownError'),\r\n        center: true,\r\n      })\r\n    })\r\n}\r\n\r\nfunction onCancel(who: string) {\r\n  myVisible.value = false\r\n  if (who === 'edit' || who === 'create') {\r\n    doEdit.value = false\r\n  }\r\n}\r\n\r\nfunction toEdit() {\r\n  doEdit.value = true\r\n}\r\n\r\nconst lableTitle = ref(t('basicInfo'))\r\n\r\nfunction parentClick(value: string) {\r\n  lableTitle.value = value\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div>\r\n    <el-dialog v-model=\"myVisible\" width=\"1000px\" :title=\"title\" :close-on-click-modal=\"false\" append-to-body :modal=\"true\" destroy-on-close>\r\n      <template #header=\"{ titleId, titleClass }\">\r\n        <h4 :id=\"titleId\" :class=\"titleClass\">\r\n          {{ title }}\r\n        </h4>\r\n      </template>\r\n      <hr class=\"title-divider\" />\r\n      <Recharge ref=\"formRef\" v-bind=\"props\" @change-str=\"parentClick\" />\r\n      <template #footer>\r\n        <el-button size=\"large\" @click=\"onCancel('create')\">\r\n          {{ t('cancel') }}\r\n        </el-button>\r\n        <el-button type=\"primary\" size=\"large\" @click=\"onSubmit\">\r\n          {{ t('save') }}\r\n        </el-button>\r\n      </template>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.title-divider {\r\n  margin-top: -20px;\r\n  margin-bottom: 30px;\r\n  border: none;\r\n  border-top: 1px solid #ccc;\r\n}\r\n</style>\r\n"], "names": ["props", "__props", "emits", "__emit", "formRef", "ref", "t", "useI18n", "myVisible", "computed", "get", "modelValue", "set", "val", "doEdit", "isEdit", "title", "handle", "onSubmit", "value", "submit", "then", "onCancel", "catch", "error", "ElMessage", "message", "center", "who", "lableTitle", "parentClick"], "mappings": "wuCA4CA,MAAMA,EAAQC,EAYRC,EAAQC,EAMRC,EAAUC,KAEVC,EAAEA,GAAMC,IAERC,EAAYC,EAAS,CACzBC,IAAM,IACGV,EAAMW,WAEf,GAAAC,CAAIC,GACFX,EAAM,oBAAqBW,EAAG,IAI5BC,EAASL,EAAS,CACtBC,IAAM,IACGV,EAAMe,OAEf,GAAAH,CAAIC,GACFX,EAAM,aAAcW,EAAG,IAIrBG,EAAQP,GAAS,IACA,WAAjBT,EAAMiB,OACDX,EAAE,kBACiB,SAAjBN,EAAMiB,OACRX,EAAE,wBAEFA,EAAE,6BAIb,SAASY,IAEPd,EAAQe,MACLC,SACAC,MAAK,KACJnB,EAAM,WACNoB,EAAS,GAAE,IAEZC,OAAOC,IACNC,EAAUD,MAAM,CACdE,QAASF,EAAME,SAAWpB,EAAE,gBAC5BqB,QAAQ,GACT,GACF,CAGL,SAASL,EAASM,GAChBpB,EAAUW,OAAQ,EACN,SAARS,GAA0B,WAARA,IACpBd,EAAOK,OAAQ,EACjB,CAOF,MAAMU,EAAaxB,EAAIC,EAAE,cAEzB,SAASwB,EAAYX,GACnBU,EAAWV,MAAQA,CAAA"}