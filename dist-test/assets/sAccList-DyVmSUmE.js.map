{"version": 3, "file": "sAccList-DyVmSUmE.js", "sources": ["../../src/views/order/search/sAccList.vue"], "sourcesContent": ["<i18n>\r\n{\r\n  \"en\": {\r\n    \"channel\": \"Channel\",\r\n    \"exactSearch\": \"Exact Search\",\r\n    \"guest\": \"Guest/Phone\",\r\n    \"name\": \"Name\",\r\n    \"phone\": \"Phone\",\r\n    \"time\":\"Time\",\r\n    \"checkOutTime\": \"Check Out Time\",\r\n    \"checkInTime\": \"Check In Time\",\r\n\t\t\"checkInOutTime\": \"In Out Time\",\r\n    \"roomType\": \"Room Type\",\r\n    \"roomNumber\": \"Room No\",\r\n    \"price\": \"Price\",\r\n    \"guestSourceType\": \"Guest Source\",\r\n    \"operation\": \"Actions\",\r\n    \"search\": {\r\n      \"all\": \"All\",\r\n      \"searchPlaceholder\": \"OrderNo、Name、Phone、Room\",\r\n      \"select\": \"select\",\r\n      \"timeRange\": {\r\n        \"start\": \"Start Time\",\r\n        \"end\": \"End Time\"\r\n      }\r\n    },\r\n    \"checkinType\": \"Check-in\",\r\n    \"query\": \"Filter\",\r\n    \"orderNo\": \"Order No\",\r\n    \"orderDetails\": \"View\",\r\n    \"to\": \"to\",\r\n\t\t\"hourToFull\": \"Hourly To Full\",\r\n    \"orderNoLabel\": \"Order No:\",\r\n    \"externalOrderNoLabel\": \"External Order No:\",\r\n    \"guestLabel\": \"Guest:\",\r\n    \"phoneLabel\": \"Phone:\",\r\n    \"checkinLabel\": \"Check-in:\",\r\n    \"checkoutLabel\": \"Check-out:\",\r\n    \"currency\": \"¥\",\r\n    \"displayWay\": \"Display Mode\",\r\n    \"orderMode\": \"Order Mode\",\r\n    \"guestMode\": \"Guest Mode\",\r\n    \"sex\": \"Gender\",\r\n    \"mainOrderStatus\": \"Is Main Guest\",\r\n    \"plannedCheckoutTime\": \"Planned Checkout\",\r\n    \"status\": \"Status\",\r\n    \"noData\": \"No Data\",\r\n    \"male\": \"Male\",\r\n    \"female\": \"Female\",\r\n    \"unknown\": \"Unknown\",\r\n    \"mainGuest\": \"Main Guest\",\r\n    \"companion\": \"Companion\",\r\n    \"orderStatus\": {\r\n      \"no_check_in\": \"Booking\",\r\n      \"check_in\": \"Checked In\",\r\n      \"check_out\": \"Checked Out\",\r\n      \"noshow\": \"No Show\",\r\n      \"cancel\": \"Cancelled\",\r\n      \"be_confirm\": \"Pending Confirmation\",\r\n      \"refuse\": \"Refused\",\r\n      \"over\": \"Completed\",\r\n      \"credit\": \"Credit\",\r\n      \"continue\": \"Extended Stay\"\r\n    }\r\n  },\r\n  \"zh-cn\": {\r\n    \"channel\": \"渠道\",\r\n    \"exactSearch\": \"精确搜索\",\r\n    \"guest\": \"客人/电话\",\r\n    \"name\": \"客人\",\r\n    \"phone\": \"电话\",\r\n    \"time\":\"时间\",\r\n    \"checkOutTime\": \"退房时间\",\r\n    \"checkInTime\": \"入住时间\",\r\n\t\t\"checkInOutTime\": \"入离时间\",\r\n    \"roomType\": \"房型\",\r\n    \"roomNumber\": \"房号\",\r\n    \"price\": \"房价\",\r\n    \"guestSourceType\": \"客源类型\",\r\n    \"operation\": \"操作\",\r\n    \"search\": {\r\n      \"select\": \"请选择\",\r\n      \"all\": \"全部\",\r\n      \"searchPlaceholder\": \"订单号、外部订单号、姓名、手机号、房号\",\r\n      \"timeRange\": {\r\n        \"start\": \"开始时间\",\r\n        \"end\": \"结束时间\"\r\n      }\r\n    },\r\n    \"checkinType\": \"入住类型\",\r\n    \"query\": \"查询\",\r\n    \"orderNo\": \"订单号/外部订单号\",\r\n    \"orderDetails\": \"查看\",\r\n    \"to\": \"到\",\r\n\t\t\"hourToFull\": \"钟点房转全天\",\r\n    \"orderNoLabel\": \"订单号：\",\r\n    \"externalOrderNoLabel\": \"外部订单号：\",\r\n    \"guestLabel\": \"客人：\",\r\n    \"phoneLabel\": \"电话：\",\r\n    \"checkinLabel\": \"入住：\",\r\n    \"checkoutLabel\": \"退房：\",\r\n    \"currency\": \"￥\",\r\n    \"displayWay\": \"展示模式\",\r\n    \"orderMode\": \"订单模式\",\r\n    \"guestMode\": \"客人模式\",\r\n    \"sex\": \"性别\",\r\n    \"mainOrderStatus\": \"是否主客\",\r\n    \"plannedCheckoutTime\": \"预离时间\",\r\n    \"status\": \"状态\",\r\n    \"noData\": \"暂无数据\",\r\n    \"male\": \"男\",\r\n    \"female\": \"女\",\r\n    \"unknown\": \"保密\",\r\n    \"mainGuest\": \"主客\",\r\n    \"companion\": \"同住\",\r\n    \"orderStatus\": {\r\n      \"no_check_in\": \"预订中\",\r\n      \"check_in\": \"已入住\",\r\n      \"check_out\": \"已退房\",\r\n      \"noshow\": \"未到店\",\r\n      \"cancel\": \"已取消\",\r\n      \"be_confirm\": \"待确认\",\r\n      \"refuse\": \"已拒绝\",\r\n      \"over\": \"已完成\",\r\n      \"credit\": \"挂账\",\r\n      \"continue\": \"续住\"\r\n    }\r\n  },\r\n  \"km\": {\r\n    \"channel\": \"ឆានែល\",\r\n    \"exactSearch\": \"ការស្វែងរកច្បាស់លាស់\",\r\n    \"guest\": \"ភ្ញៀវ/ទូរស័ព្ទ\",\r\n    \"name\": \"ឈ្មោះ\",\r\n    \"phone\": \"ទូរស័ព្ទ\",\r\n    \"time\": \"ពេលវេលា\",\r\n    \"checkOutTime\": \"ពេលវេលាចាកចេញ\",\r\n    \"checkInTime\": \"ពេលវេលាចូលស្នាក់នៅ\",\r\n    \"checkInOutTime\": \"ពេលវេលាចូល/ចាកចេញ\",\r\n    \"roomType\": \"ប្រភេទបន្ទប់\",\r\n    \"roomNumber\": \"លេខបន្ទប់\",\r\n    \"price\": \"តម្លៃ\",\r\n    \"guestSourceType\": \"ប្រភពភ្ញៀវ\",\r\n    \"operation\": \"សកម្មភាព\",\r\n    \"search\": {\r\n      \"all\": \"ទាំងអស់\",\r\n      \"searchPlaceholder\": \"លេខបញ្ជាទិញ, ឈ្មោះ, ទូរស័ព្ទ, លេខបន្ទប់\",\r\n      \"select\": \"ជ្រើសរើស\",\r\n      \"timeRange\": {\r\n        \"start\": \"ពេលវេលាចាប់ផ្តើម\",\r\n        \"end\": \"ពេលវេលាបញ្ចប់\"\r\n      }\r\n    },\r\n    \"checkinType\": \"ប្រភេទចូលស្នាក់នៅ\",\r\n    \"query\": \"ស្វែងរក\",\r\n    \"orderNo\": \"លេខបញ្ជាទិញ\",\r\n    \"orderDetails\": \"មើល\",\r\n    \"to\": \"ទៅ\",\r\n    \"hourToFull\": \"បន្ទប់ម៉ោងទៅពេញម៉ោង\",\r\n    \"orderNoLabel\": \"លេខបញ្ជាទិញ៖\",\r\n    \"externalOrderNoLabel\": \"លេខបញ្ជាទិញខាងក្រៅ៖\",\r\n    \"guestLabel\": \"ភ្ញៀវ៖\",\r\n    \"phoneLabel\": \"ទូរស័ព្ទ៖\",\r\n    \"checkinLabel\": \"ចូលស្នាក់នៅ៖\",\r\n    \"checkoutLabel\": \"ចាកចេញ៖\",\r\n    \"currency\": \"៛\",\r\n    \"displayWay\": \"របៀបបង្ហាញ\",\r\n    \"orderMode\": \"របៀបបញ្ជាទិញ\",\r\n    \"guestMode\": \"របៀបភ្ញៀវ\",\r\n    \"sex\": \"ភេទ\",\r\n    \"mainOrderStatus\": \"ស្ថានភាពបញ្ជាទិញសំខាន់\",\r\n    \"plannedCheckoutTime\": \"ពេលវេលាចាកចេញគ្រោងទុក\",\r\n    \"status\": \"ស្ថានភាព\",\r\n    \"noData\": \"គ្មានទិន្នន័យ\",\r\n    \"male\": \"ប្រុស\",\r\n    \"female\": \"ស្រី\",\r\n    \"unknown\": \"មិនស្គាល់\",\r\n    \"mainGuest\": \"ភ្ញៀវសំខាន់\",\r\n    \"companion\": \"ភ្ញៀវអមដំណើរ\",\r\n    \"orderStatus\": {\r\n      \"no_check_in\": \"កំពុងកក់\",\r\n      \"check_in\": \"បានចូលស្នាក់នៅ\",\r\n      \"check_out\": \"បានចាកចេញ\",\r\n      \"noshow\": \"មិនបានមកដល់\",\r\n      \"cancel\": \"បានលុបចោល\",\r\n      \"be_confirm\": \"រង់ចាំការបញ្ជាក់\",\r\n      \"refuse\": \"បានបដិសេធ\",\r\n      \"over\": \"បានបញ្ចប់\",\r\n      \"credit\": \"ជំពាក់\",\r\n      \"continue\": \"បន្តស្នាក់នៅ\"\r\n    }\r\n  }\r\n}\r\n</i18n>\r\n\r\n<script setup lang=\"ts\">\r\nimport type { OrderHisModel } from '@/models/index'\r\nimport { channelApi, dictDataApi, orderApi, rtApi } from '@/api/modules/index'\r\n\r\nimport { BooleanEnum, DICT_TYPE_CHECKIN_TYPE, DICT_TYPE_GUEST_SRC_TYPE, NoType, OrderState } from '@/models/index'\r\nimport useUserStore from '@/store/modules/user'\r\nimport ymdate from '@/utils/timeutils'\r\nimport OrderDialog from '@/views/order/info/order.vue'\r\nimport { Search } from '@element-plus/icons-vue'\r\nimport dayjs from 'dayjs'\r\n\r\nconst userStore = useUserStore()\r\nconst { t } = useI18n()\r\n\r\nconst { pagination, getParams, onSizeChange, onCurrentChange, onSortChange } = usePagination()\r\n\r\nconst data = ref({\r\n  loading: false,\r\n  tableAutoHeight: false,\r\n  formMode: 'dialog' as 'router' | 'dialog' | 'drawer',\r\n  formModeProps: {\r\n    orderNo: '',\r\n  },\r\n  search: {\r\n    channelCode: '-1',\r\n    rtCode: '',\r\n    guestSrcType: '',\r\n    checkinType: '',\r\n    seller: '',\r\n    searchType: '0',\r\n    searchContent: '',\r\n    dataType: '0',\r\n    dataTime: '',\r\n    // 显示方式, 0: 订单模式,1：客人模式\r\n    displayWay: '0',\r\n  },\r\n  dataList: [] as OrderHisModel[],\r\n})\r\n\r\nonMounted(() => {\r\n  getChannels()\r\n  getRts()\r\n  getConstants()\r\n  getDataList()\r\n})\r\n\r\nconst channels = ref<{ channelCode: string; channelName: string }[]>([])\r\nfunction getChannels() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    isEnable: BooleanEnum.YES,\r\n  }\r\n  channelApi.getChannelSimpleList(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      channels.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\nconst rts = ref<{ rtCode: string; rtName: string }[]>([])\r\nfunction getRts() {\r\n  const params = {\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    isVirtual: BooleanEnum.NO,\r\n    isEnable: BooleanEnum.YES,\r\n  }\r\n  rtApi.getRoomTypeSimpleList(params).then((res: any) => {\r\n    if (res.code === 0) {\r\n      rts.value = res.data\r\n    }\r\n  })\r\n}\r\n\r\nconst dictTypes = [DICT_TYPE_GUEST_SRC_TYPE, DICT_TYPE_CHECKIN_TYPE]\r\nconst srcTypeList = ref<{ code: string; label: string }[]>([])\r\nconst checkinTypeList = ref<{ code: string; label: string }[]>([])\r\nfunction getConstants() {\r\n  dictDataApi.getDictDataBatch(dictTypes).then((res: any) => {\r\n    srcTypeList.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_GUEST_SRC_TYPE)\r\n    checkinTypeList.value = res.data.filter((item: any) => item.dictType === DICT_TYPE_CHECKIN_TYPE)\r\n  })\r\n}\r\n\r\nfunction getDataList() {\r\n  data.value.loading = true\r\n  const params = {\r\n    ...getParams(),\r\n    gcode: userStore.gcode,\r\n    hcode: userStore.hcode,\r\n    keyWords: data.value.search.searchContent,\r\n    channelCode: data.value.search.channelCode === '-1' ? '' : data.value.search.channelCode,\r\n    timeType: data.value.search.dataType,\r\n    startTime: data.value.search.dataTime ? ymdate(data.value.search.dataTime[0]) : '',\r\n    endTime: data.value.search.dataTime ? ymdate(data.value.search.dataTime[1]) : '',\r\n    guestSrcType: data.value.search.guestSrcType,\r\n    rtCode: data.value.search.rtCode,\r\n    checkinType: data.value.search.checkinType,\r\n    displayWay: data.value.search.displayWay,\r\n  }\r\n  orderApi.pendingAccountList(params).then((res: any) => {\r\n    data.value.loading = false\r\n    if (res.data.list) {\r\n      data.value.dataList = res.data.list\r\n      pagination.value.total = res.data.total\r\n    }\r\n  })\r\n}\r\n\r\nfunction sizeChange(size: number) {\r\n  onSizeChange(size).then(() => getDataList())\r\n}\r\n\r\nfunction currentChange(page = 1) {\r\n  onCurrentChange(page).then(() => getDataList())\r\n}\r\n\r\nfunction sortChange({ prop, order }: { prop: string; order: string }) {\r\n  onSortChange(prop, order).then(() => getDataList())\r\n}\r\n\r\nconst detailVisible = ref(false)\r\nconst routerName = ref('detail')\r\nconst typeName = ref('individual')\r\nfunction onDetail(row: any) {\r\n  data.value.formModeProps.orderNo = row.orderNo\r\n  detailVisible.value = true\r\n}\r\n\r\n/**\r\n * 获取订单状态的显示文本\r\n * @param state 订单状态代码\r\n * @returns 状态显示文本\r\n */\r\nfunction getOrderStatusText(state: string): string {\r\n  return t(`orderStatus.${state}`) || state\r\n}\r\n\r\n/**\r\n * 获取订单状态的标签类型\r\n * @param state 订单状态代码\r\n * @returns Element Plus 标签类型\r\n */\r\nfunction getOrderStatusType(state: string): 'success' | 'info' | 'warning' | 'danger' | '' {\r\n  switch (state) {\r\n    case OrderState.CHECK_IN:\r\n      return 'success'\r\n    case OrderState.CHECK_OUT:\r\n      return 'info'\r\n    case OrderState.IN_BOOKING:\r\n      return 'warning'\r\n    case OrderState.CANCEL:\r\n    case OrderState.REFUSE:\r\n    case OrderState.NOSHOW:\r\n      return 'danger'\r\n    case OrderState.OVER:\r\n    case OrderState.BE_CONFIRM:\r\n      return ''\r\n    case OrderState.CREDIT:\r\n    case OrderState.CONTINUE:\r\n      return 'warning'\r\n    default:\r\n      return ''\r\n  }\r\n}\r\n</script>\r\n\r\n<template>\r\n  <div :class=\"{ 'absolute-container': data.tableAutoHeight }\">\r\n    <page-main>\r\n      <search-bar :show-toggle=\"false\">\r\n        <el-form :model=\"data.search\" size=\"default\" label-width=\"80px\" inline-message inline class=\"search-form\">\r\n          <div class=\"filter-row\">\r\n            <el-form-item :label=\"t('channel')\">\r\n              <el-select v-model=\"data.search.channelCode\" clearable class=\"filter-select\">\r\n                <el-option :label=\"t('search.all')\" value=\"-1\" />\r\n                <el-option v-for=\"item in channels\" :key=\"item.channelCode\" :label=\"item.channelName\" :value=\"item.channelCode\" />\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item :label=\"t('guestSourceType')\">\r\n              <el-select v-model=\"data.search.guestSrcType\" clearable class=\"filter-select\">\r\n                <el-option v-for=\"item in srcTypeList\" :key=\"item.code\" :label=\"item.label\" :value=\"item.code\" />\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item :label=\"t('roomType')\">\r\n              <el-select v-model=\"data.search.rtCode\" clearable class=\"filter-select\">\r\n                <el-option v-for=\"item in rts\" :key=\"item.rtCode\" :label=\"item.rtName\" :value=\"item.rtCode\" />\r\n              </el-select>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <div class=\"filter-row\">\r\n            <el-form-item :label=\"t('checkinType')\">\r\n              <el-select v-model=\"data.search.checkinType\" clearable class=\"filter-select\" :placeholder=\"t('search.select')\">\r\n                <el-option v-for=\"item in checkinTypeList\" :key=\"item.code\" :label=\"item.label\" :value=\"item.code\" />\r\n              </el-select>\r\n            </el-form-item>\r\n\r\n            <el-form-item :label=\"t('exactSearch')\">\r\n              <el-input v-model=\"data.search.searchContent\" class=\"filter-select w-350px!\" clearable :placeholder=\"t('search.searchPlaceholder')\" @clear=\"currentChange()\" @keydown.enter=\"currentChange()\">\r\n                <template #append>\r\n                  <el-button :icon=\"Search\" @click=\"currentChange()\" />\r\n                </template>\r\n              </el-input>\r\n            </el-form-item>\r\n          </div>\r\n\r\n          <div class=\"filter-row filter-items-row\">\r\n            <el-form-item :label=\"t('time')\" class=\"search-input-item\">\r\n              <el-select v-model=\"data.search.dataType\" style=\"width: 120px\">\r\n                <el-option :label=\"t('checkOutTime')\" value=\"0\" />\r\n                <el-option :label=\"t('checkInTime')\" value=\"1\" />\r\n              </el-select>\r\n              <el-date-picker v-model=\"data.search.dataTime\" class=\"date-picker\" type=\"daterange\" :range-separator=\"t('to')\" :start-placeholder=\"t('search.timeRange.start')\" :end-placeholder=\"t('search.timeRange.end')\" />\r\n            </el-form-item>\r\n            <el-form-item class=\"search-btn\">\r\n              <el-button type=\"primary\" @click=\"getDataList\">\r\n                {{ t('query') }}\r\n              </el-button>\r\n            </el-form-item>\r\n            <el-form-item class=\"switch-form-item\">\r\n              <div class=\"switch-container\">\r\n                <el-switch\r\n                  v-model=\"data.search.displayWay\"\r\n                  active-value=\"1\"\r\n                  inactive-value=\"0\"\r\n                  :active-text=\"t('guestMode')\"\r\n                  :inactive-text=\"t('orderMode')\"\r\n                  inline-prompt\r\n                  class=\"display-way-switch\"\r\n                  style=\"--el-switch-on-color: #13ce66; --el-switch-off-color: #554dd6\"\r\n                />\r\n              </div>\r\n            </el-form-item>\r\n          </div>\r\n        </el-form>\r\n      </search-bar>\r\n\r\n      <el-table v-loading=\"data.loading\" class=\"list-table\" :header-cell-style=\"{ background: '#f5f7fa', color: '#606266' }\" :data=\"data.dataList\" stripe highlight-current-row border height=\"100%\" @sort-change=\"sortChange\">\r\n        <!-- 可展开行列 - 仅在订单模式下显示 -->\r\n        <el-table-column v-if=\"data.search.displayWay === '0'\" type=\"expand\" width=\"50\">\r\n          <template #default=\"{ row }\">\r\n            <div class=\"expand-content\">\r\n              <el-table v-if=\"row.togetherList && row.togetherList.length > 0\" :data=\"row.togetherList\" border size=\"small\" class=\"expand-table\">\r\n                <el-table-column prop=\"name\" :label=\"t('name')\" min-width=\"120\" />\r\n                <el-table-column prop=\"phone\" :label=\"t('phone')\" min-width=\"120\" />\r\n                <el-table-column :label=\"t('sex')\" width=\"80\">\r\n                  <template #default=\"scope\">\r\n                    <el-tag :type=\"scope.row.sex === '1' ? 'primary' : 'danger'\" size=\"small\">\r\n                      {{ scope.row.sex === '1' ? t('male') : scope.row.sex === '0' ? t('female') : t('unknown') }}\r\n                    </el-tag>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column :label=\"t('mainOrderStatus')\" min-width=\"120\">\r\n                  <template #default=\"scope\">\r\n                    <el-tag :type=\"scope.row.isMain === '1' || row.togetherList.length === 1 ? 'success' : 'info'\" size=\"small\">\r\n                      {{ scope.row.isMain === '1' || row.togetherList.length === 1 ? t('mainGuest') : t('companion') }}\r\n                    </el-tag>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column :label=\"t('checkInTime')\" min-width=\"120\">\r\n                  <template #default=\"scope\">\r\n                    {{ scope.row.checkinTime ? dayjs(scope.row.checkinTime).format('MM-DD HH:mm') : '-' }}\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column :label=\"t('checkOutTime')\" min-width=\"120\">\r\n                  <template #default=\"scope\">\r\n                    {{ scope.row.checkoutTime ? dayjs(scope.row.checkoutTime).format('MM-DD HH:mm') : '-' }}\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column :label=\"t('plannedCheckoutTime')\" min-width=\"120\">\r\n                  <template #default=\"scope\">\r\n                    {{ scope.row.planCheckoutTime ? dayjs(scope.row.planCheckoutTime).format('MM-DD HH:mm') : '-' }}\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column :label=\"t('status')\" width=\"100\">\r\n                  <template #default=\"scope\">\r\n                    <el-tag :type=\"getOrderStatusType(scope.row.state)\" size=\"small\">\r\n                      {{ getOrderStatusText(scope.row.state) }}\r\n                    </el-tag>\r\n                  </template>\r\n                </el-table-column>\r\n              </el-table>\r\n              <div v-else class=\"no-data\">\r\n                {{ t('noData') }}\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('orderNo')\" min-width=\"190\">\r\n          <template #default=\"scope\">\r\n            <div class=\"order-info\">\r\n              <div class=\"info-row\">\r\n                <span class=\"label\">{{ t('orderNoLabel') }}</span>\r\n                <span class=\"value\">{{ scope.row.orderNo }}</span>\r\n              </div>\r\n              <div v-if=\"scope.row.outOrderNo\" class=\"info-row\">\r\n                <span class=\"label\">{{ t('externalOrderNoLabel') }}</span>\r\n                <span class=\"value\">{{ scope.row.outOrderNo }}</span>\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('guest')\" min-width=\"150\">\r\n          <template #default=\"scope\">\r\n            <div class=\"guest-info\">\r\n              <div class=\"info-row\">\r\n                <span class=\"label\">{{ t('guestLabel') }}</span>\r\n                <span class=\"value\">{{ scope.row.name }}</span>\r\n              </div>\r\n              <div v-if=\"scope.row.phone\" class=\"info-row\">\r\n                <span class=\"label\">{{ t('phoneLabel') }}</span>\r\n                <span class=\"value\">{{ scope.row.phone }}</span>\r\n              </div>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('roomType')\" min-width=\"180\">\r\n          <template #default=\"scope\">\r\n            <div class=\"room-info\">\r\n              <span class=\"room-type\">{{ scope.row.rtName }}</span>\r\n              <span class=\"price\">{{ t('currency') }}{{ scope.row.price ?? '-' }}</span>\r\n            </div>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"rNo\" :label=\"t('roomNumber')\" min-width=\"90\" sortable=\"custom\" />\r\n        <el-table-column :label=\"t('checkInTime')\" prop=\"checkinTime\" sortable=\"custom\" min-width=\"120\">\r\n          <template #default=\"scope\">\r\n            {{ dayjs(scope.row.checkinTime).format('MM-DD HH:mm') }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('checkOutTime')\" prop=\"checkoutTime\" sortable=\"custom\" min-width=\"120\">\r\n          <template #default=\"scope\">\r\n            {{ dayjs(scope.row.checkoutTime).format('MM-DD HH:mm') }}\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column :label=\"t('checkinType')\" min-width=\"110\">\r\n          <template #default=\"scope\">\r\n            {{ scope.row.checkinTypeName }}\r\n            <span v-if=\"scope.row.hourToFull === BooleanEnum.YES\"><br />({{ t('hourToFull') }})</span>\r\n          </template>\r\n        </el-table-column>\r\n        <el-table-column prop=\"guestSrcTypeName\" :label=\"t('guestSourceType')\" min-width=\"120\" />\r\n        <el-table-column prop=\"channelName\" :label=\"t('channel')\" min-width=\"100\" />\r\n        <el-table-column :label=\"t('operation')\" align=\"center\" fixed=\"right\" width=\"100\">\r\n          <template #default=\"scope\">\r\n            <el-link v-auth=\"'pms:order:query:get-order-detail'\" type=\"primary\" @click=\"onDetail(scope.row)\">\r\n              {{ t('orderDetails') }}\r\n            </el-link>\r\n          </template>\r\n        </el-table-column>\r\n      </el-table>\r\n      <el-pagination\r\n        v-if=\"pagination.total > 10\"\r\n        :current-page=\"pagination.pageNo\"\r\n        :total=\"pagination.total\"\r\n        :page-size=\"pagination.pageSize\"\r\n        :page-sizes=\"pagination.sizes\"\r\n        :layout=\"pagination.layout\"\r\n        :hide-on-single-page=\"false\"\r\n        class=\"pagination\"\r\n        background\r\n        @size-change=\"sizeChange\"\r\n        @current-change=\"currentChange\"\r\n      />\r\n      <OrderDialog v-if=\"detailVisible\" v-model=\"detailVisible\" :no=\"data.formModeProps.orderNo\" :no-type=\"NoType.ORDER\" :tab-name=\"routerName\" :tab-type=\"typeName\" @reload=\"getDataList\" />\r\n    </page-main>\r\n  </div>\r\n</template>\r\n\r\n<style lang=\"scss\" scoped>\r\n.el-link {\r\n  margin: 0 10px;\r\n}\r\n\r\n.absolute-container {\r\n  position: absolute;\r\n  display: flex;\r\n  flex-direction: column;\r\n  width: 100%;\r\n  height: 100%;\r\n\r\n  .page-header {\r\n    margin-bottom: 0;\r\n  }\r\n\r\n  .page-main {\r\n    display: flex;\r\n    flex: 1;\r\n    flex-direction: column;\r\n    overflow: auto;\r\n\r\n    .search-container {\r\n      margin-bottom: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.page-main {\r\n  .search-form {\r\n    width: 100%;\r\n\r\n    .filter-row {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      margin-bottom: 8px;\r\n\r\n      &.quick-filter-row {\r\n        margin-bottom: 12px;\r\n      }\r\n\r\n      &.filter-items-row {\r\n        justify-content: flex-start;\r\n        gap: 0;\r\n\r\n        .el-form-item {\r\n          width: auto;\r\n          margin-right: 12px;\r\n          margin-bottom: 8px;\r\n\r\n          &:nth-child(3n) {\r\n            margin-right: 0;\r\n          }\r\n        }\r\n      }\r\n\r\n      .el-form-item {\r\n        margin-bottom: 0;\r\n\r\n        .filter-select {\r\n          width: 200px; /* 减小宽度 */\r\n        }\r\n\r\n        /* 减小表单项内部元素的垂直间距 */\r\n        :deep(.el-form-item__label) {\r\n          white-space: nowrap;\r\n          min-width: 80px;\r\n          width: auto !important;\r\n        }\r\n\r\n        :deep(.el-form-item__content) {\r\n          line-height: 28px;\r\n        }\r\n      }\r\n\r\n      .date-type-item {\r\n        width: auto !important;\r\n\r\n        .date-type-select {\r\n          width: 150px;\r\n        }\r\n      }\r\n\r\n      &.filter-items-row {\r\n        justify-content: flex-start;\r\n        gap: 0;\r\n\r\n        .el-form-item {\r\n          width: auto;\r\n          margin-right: 12px;\r\n          margin-bottom: 8px;\r\n\r\n          &:nth-child(3n) {\r\n            margin-right: 0;\r\n          }\r\n\r\n          &.search-input-item,\r\n          &.search-btn {\r\n            :deep(.el-form-item__content) {\r\n              display: flex;\r\n              align-items: center;\r\n              gap: 8px;\r\n            }\r\n          }\r\n\r\n          &.switch-form-item {\r\n            flex: 1;\r\n            display: flex;\r\n            justify-content: flex-end;\r\n\r\n            :deep(.el-form-item__content) {\r\n              display: flex;\r\n              justify-content: flex-end;\r\n              width: 100%;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .el-divider {\r\n    width: calc(100% + 40px);\r\n    margin-inline: -20px;\r\n  }\r\n\r\n  .pagination {\r\n    margin-top: 16px;\r\n    display: flex;\r\n    justify-content: flex-end;\r\n  }\r\n}\r\n\r\n.flex-form {\r\n  display: flex;\r\n}\r\n\r\n// 表格工具栏样式\r\n.table-toolbar {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  padding: 12px 0;\r\n  border-bottom: 1px solid #e4e7ed;\r\n  margin-bottom: 12px;\r\n\r\n  .toolbar-left {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 12px;\r\n\r\n    .toolbar-label {\r\n      font-size: 14px;\r\n      color: #606266;\r\n      font-weight: 500;\r\n    }\r\n\r\n    .display-way-switch {\r\n      :deep(.el-switch__label) {\r\n        color: #606266;\r\n        font-size: 13px;\r\n      }\r\n\r\n      :deep(.el-switch__label.is-active) {\r\n        color: #409eff;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// 可展开行样式\r\n.expand-content {\r\n  padding: 16px;\r\n  background-color: #f8f9fa;\r\n\r\n  .expand-title {\r\n    font-size: 14px;\r\n    font-weight: 500;\r\n    color: #303133;\r\n    margin-bottom: 12px;\r\n    padding-bottom: 8px;\r\n    border-bottom: 1px solid #e4e7ed;\r\n  }\r\n\r\n  .expand-table {\r\n    :deep(.el-table__header) {\r\n      background-color: #fafafa;\r\n    }\r\n\r\n    :deep(.el-table__body) {\r\n      background-color: #fff;\r\n    }\r\n\r\n    :deep(.el-table td) {\r\n      padding: 8px 0;\r\n    }\r\n\r\n    :deep(.el-table th) {\r\n      padding: 8px 0;\r\n      background-color: #fafafa !important;\r\n    }\r\n  }\r\n\r\n  .no-data {\r\n    text-align: center;\r\n    color: #909399;\r\n    padding: 20px;\r\n    font-size: 14px;\r\n  }\r\n}\r\n\r\n.list-table {\r\n  .order-info,\r\n  .guest-info,\r\n  .time-info {\r\n    .info-row {\r\n      display: flex;\r\n      align-items: center;\r\n      line-height: 20px;\r\n      margin-bottom: 4px;\r\n\r\n      &:last-child {\r\n        margin-bottom: 0;\r\n      }\r\n\r\n      .label {\r\n        color: #909399;\r\n        flex-shrink: 0;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .value {\r\n        flex: 1;\r\n      }\r\n    }\r\n  }\r\n\r\n  .room-info {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 8px;\r\n\r\n    .room-type {\r\n      color: #606266;\r\n    }\r\n\r\n    .price {\r\n      color: #f56c6c;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n</style>\r\n"], "names": ["userStore", "useUserStore", "t", "useI18n", "pagination", "getParams", "onSizeChange", "onCurrentChange", "onSortChange", "usePagination", "data", "ref", "loading", "tableAutoHeight", "formMode", "formModeProps", "orderNo", "search", "channelCode", "rtCode", "guestSrcType", "checkinType", "seller", "searchType", "searchContent", "dataType", "dataTime", "displayWay", "dataList", "onMounted", "params", "gcode", "hcode", "isEnable", "BooleanEnum", "YES", "channelApi", "getChannelSimpleList", "then", "res", "code", "channels", "value", "getChannels", "isVirtual", "NO", "rtApi", "getRoomTypeSimpleList", "rts", "getRts", "dict<PERSON>ata<PERSON><PERSON>", "getDictDataBatch", "dictTypes", "srcTypeList", "filter", "item", "dictType", "DICT_TYPE_GUEST_SRC_TYPE", "checkinTypeList", "DICT_TYPE_CHECKIN_TYPE", "getDataList", "key<PERSON>ords", "timeType", "startTime", "ymdate", "endTime", "orderApi", "pendingAccountList", "list", "total", "sizeChange", "size", "currentChange", "page", "sortChange", "prop", "order", "detailVisible", "routerName", "typeName", "getOrderStatusType", "state", "OrderState", "CHECK_IN", "CHECK_OUT", "IN_BOOKING", "CANCEL", "REFUSE", "NOSHOW", "OVER", "BE_CONFIRM", "CREDIT", "CONTINUE", "row"], "mappings": "wpLA6MA,MAAMA,EAAYC,KACZC,EAAEA,IAAMC,KAERC,WAAEA,GAAYC,UAAAA,GAAAC,aAAWA,mBAAcC,GAAiBC,aAAAA,IAAiBC,IAEzEC,GAAOC,EAAI,CACfC,SAAS,EACTC,iBAAiB,EACjBC,SAAU,SACVC,cAAe,CACbC,QAAS,IAEXC,OAAQ,CACNC,YAAa,KACbC,OAAQ,GACRC,aAAc,GACdC,YAAa,GACbC,OAAQ,GACRC,WAAY,IACZC,cAAe,GACfC,SAAU,IACVC,SAAU,GAEVC,WAAY,KAEdC,SAAU,KAGZC,GAAU,MAQV,WACE,MAAMC,EAAS,CACbC,MAAO/B,EAAU+B,MACjBC,MAAOhC,EAAUgC,MACjBC,SAAUC,EAAYC,KAExBC,EAAWC,qBAAqBP,GAAQQ,MAAMC,IAC3B,IAAbA,EAAIC,OACNC,GAASC,MAAQH,EAAI7B,KAAA,GAExB,CAjBWiC,GAqBd,WACE,MAAMb,EAAS,CACbC,MAAO/B,EAAU+B,MACjBC,MAAOhC,EAAUgC,MACjBY,UAAWV,EAAYW,GACvBZ,SAAUC,EAAYC,KAExBW,EAAMC,sBAAsBjB,GAAQQ,MAAMC,IACvB,IAAbA,EAAIC,OACNQ,GAAIN,MAAQH,EAAI7B,KAAA,GAEnB,CA/BMuC,GAsCPC,EAAYC,iBAAiBC,IAAWd,MAAMC,IAChCc,GAAAX,MAAQH,EAAI7B,KAAK4C,QAAQC,GAAcA,EAAKC,WAAaC,IACrDC,GAAAhB,MAAQH,EAAI7B,KAAK4C,QAAQC,GAAcA,EAAKC,WAAaG,GAAsB,IAtCrFC,IAAA,IAGR,MAAAnB,GAAW9B,EAAoD,IAc/D,MAAAqC,GAAMrC,EAA0C,IAehD,MAAAyC,GAAY,CAACK,EAA0BE,GACvCN,GAAc1C,EAAuC,IACrD+C,GAAkB/C,EAAuC,IAQ/D,SAASiD,KACPlD,GAAKgC,MAAM9B,SAAU,EACrB,MAAMkB,EAAS,IACVzB,KACH0B,MAAO/B,EAAU+B,MACjBC,MAAOhC,EAAUgC,MACjB6B,SAAUnD,GAAKgC,MAAMzB,OAAOO,cAC5BN,YAA+C,OAAlCR,GAAKgC,MAAMzB,OAAOC,YAAuB,GAAKR,GAAKgC,MAAMzB,OAAOC,YAC7E4C,SAAUpD,GAAKgC,MAAMzB,OAAOQ,SAC5BsC,UAAWrD,GAAKgC,MAAMzB,OAAOS,SAAWsC,EAAOtD,GAAKgC,MAAMzB,OAAOS,SAAS,IAAM,GAChFuC,QAASvD,GAAKgC,MAAMzB,OAAOS,SAAWsC,EAAOtD,GAAKgC,MAAMzB,OAAOS,SAAS,IAAM,GAC9EN,aAAcV,GAAKgC,MAAMzB,OAAOG,aAChCD,OAAQT,GAAKgC,MAAMzB,OAAOE,OAC1BE,YAAaX,GAAKgC,MAAMzB,OAAOI,YAC/BM,WAAYjB,GAAKgC,MAAMzB,OAAOU,YAEhCuC,EAASC,mBAAmBrC,GAAQQ,MAAMC,IACxC7B,GAAKgC,MAAM9B,SAAU,EACjB2B,EAAI7B,KAAK0D,OACN1D,GAAAgC,MAAMd,SAAWW,EAAI7B,KAAK0D,KACpBhE,GAAAsC,MAAM2B,MAAQ9B,EAAI7B,KAAK2D,MAAA,GAErC,CAGH,SAASC,GAAWC,GAClBjE,GAAaiE,GAAMjC,MAAK,IAAMsB,MAAa,CAGpC,SAAAY,GAAcC,EAAO,GAC5BlE,GAAgBkE,GAAMnC,MAAK,IAAMsB,MAAa,CAGhD,SAASc,IAAWC,KAAEA,EAAMC,MAAAA,IAC1BpE,GAAamE,EAAMC,GAAOtC,MAAK,IAAMsB,MAAa,CAG9C,MAAAiB,GAAgBlE,GAAI,GACpBmE,GAAanE,EAAI,UACjBoE,GAAWpE,EAAI,cAoBrB,SAASqE,GAAmBC,GAC1B,OAAQA,GACN,KAAKC,EAAWC,SACP,MAAA,UACT,KAAKD,EAAWE,UACP,MAAA,OACT,KAAKF,EAAWG,WACP,MAAA,UACT,KAAKH,EAAWI,OAChB,KAAKJ,EAAWK,OAChB,KAAKL,EAAWM,OACP,MAAA,SACT,KAAKN,EAAWO,KAChB,KAAKP,EAAWQ,WACP,MAAA,GACT,KAAKR,EAAWS,OAChB,KAAKT,EAAWU,SACP,MAAA,UACT,QACS,MAAA,GACX,8gLA7B0BX,cACnB/E,GAAE,eAAe+E,MAAYA,QADtC,IAA4BA,s5DAVVY,QACXnF,GAAAgC,MAAM3B,cAAcC,QAAU6E,EAAI7E,aACvC6D,GAAcnC,OAAQ,GAFxB,IAAkBmD"}