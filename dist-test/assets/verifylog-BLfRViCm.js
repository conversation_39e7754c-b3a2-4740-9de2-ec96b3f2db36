import{d as t,aj as e,b as a,B as i,y as r,aq as s,u as l,o,c as f,f as c,w as n,e as u,h as m,Y as d,i as p,aR as b,t as w,q as O,v as _,ay as g,aT as j}from"./index-CkEhI1Zk.js";/* empty css                   *//* empty css                  *//* empty css                   *//* empty css                        *//* empty css                    */import"./el-tooltip-l0sNRNKZ.js";/* empty css                  *//* empty css                     *//* empty css                  *//* empty css               */import{_ as h}from"./_plugin-vue_export-helper-BCo6x5W8.js";const v=t({__name:"verifylog",props:{modelValue:{type:Boolean,default:!1},arSetCode:{default:""},accNos:{}},emits:["update:modelValue","success"],setup(t,{emit:h}){const v=t,k=h,{t:y}=e(),V=a(!1),R=a({arSetCode:v.arSetCode,dataList:[]}),S=i({get:()=>v.modelValue,set(t){k("update:modelValue",t)}});function T(t){b({type:"warning",message:y("writeOffSuccess")}),S.value=!1}return r((()=>{})),(t,e)=>{const a=w,i=O,r=_,b=g,h=j;return s((o(),f("div",null,[c(b,{modelValue:l(S),"onUpdate:modelValue":e[0]||(e[0]=t=>p(S)?S.value=t:null),title:l(y)("writeOffRecord"),width:"1000px","close-on-click-modal":!1,"append-to-body":"","destroy-on-close":""},{default:n((()=>[s((o(),u(r,{class:"list-table",data:l(R).dataList,"highlight-current-row":"",height:"100%",style:{"margin-top":"0"}},{default:n((()=>[c(a,{prop:"creator",label:l(y)("verifier")},null,8,["label"]),c(a,{label:l(y)("writeOffAmount"),align:"right"},{default:n((({row:t})=>[m(" ￥"+d(t.fee),1)])),_:1},8,["label"]),c(a,{label:l(y)("writeOffTime")},{default:n((({row:t})=>[m(d(t.createTime),1)])),_:1},8,["label"]),c(a,{prop:"remark",label:l(y)("remark")},null,8,["label"]),c(a,{label:l(y)("operation")},{default:n((({row:t})=>[c(i,{type:"text",onClick:t=>T()},{default:n((()=>[m(d(l(y)("cancelWriteOff")),1)])),_:2},1032,["onClick"])])),_:1},8,["label"])])),_:1},8,["data"])),[[h,l(V)]])])),_:1},8,["modelValue","title"])])),[[h,l(V)]])}}});function k(t){const e=t;e.__i18n=e.__i18n||[],e.__i18n.push({locale:"",resource:{en:{writeOffSuccess:{t:0,b:{t:2,i:[{t:3}],s:"Write-off successful"}},writeOffRecord:{t:0,b:{t:2,i:[{t:3}],s:"Record"}},verifier:{t:0,b:{t:2,i:[{t:3}],s:"Verifier"}},writeOffAmount:{t:0,b:{t:2,i:[{t:3}],s:"Amount"}},writeOffTime:{t:0,b:{t:2,i:[{t:3}],s:"Time"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"Remark"}},operation:{t:0,b:{t:2,i:[{t:3}],s:"Operation"}},cancelWriteOff:{t:0,b:{t:2,i:[{t:3}],s:"Cancel"}}},"zh-cn":{writeOffSuccess:{t:0,b:{t:2,i:[{t:3}],s:"核销成功"}},writeOffRecord:{t:0,b:{t:2,i:[{t:3}],s:"核销记录"}},verifier:{t:0,b:{t:2,i:[{t:3}],s:"核销人"}},writeOffAmount:{t:0,b:{t:2,i:[{t:3}],s:"核销金额"}},writeOffTime:{t:0,b:{t:2,i:[{t:3}],s:"核销时间"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"备注"}},operation:{t:0,b:{t:2,i:[{t:3}],s:"操作"}},cancelWriteOff:{t:0,b:{t:2,i:[{t:3}],s:"撤销核销"}}},km:{writeOffSuccess:{t:0,b:{t:2,i:[{t:3}],s:"ការលុបចោលដោយជោគជ័យ"}},writeOffRecord:{t:0,b:{t:2,i:[{t:3}],s:"កំណត់ត្រាការលុបចោល"}},verifier:{t:0,b:{t:2,i:[{t:3}],s:"អ្នកផ្ទៀងផ្ទាត់"}},writeOffAmount:{t:0,b:{t:2,i:[{t:3}],s:"ចំនួនទឹកប្រាក់ដែលត្រូវលុបចោល"}},writeOffTime:{t:0,b:{t:2,i:[{t:3}],s:"ពេលវេលាលុបចោល"}},remark:{t:0,b:{t:2,i:[{t:3}],s:"ចំណាំ"}},operation:{t:0,b:{t:2,i:[{t:3}],s:"ប្រតិបត្តិការ"}},cancelWriteOff:{t:0,b:{t:2,i:[{t:3}],s:"បោះបង់ការលុបចោល"}}}}})}k(v);const y=h(v,[["__scopeId","data-v-c8d31c76"]]);export{y as default};
//# sourceMappingURL=verifylog-BLfRViCm.js.map
