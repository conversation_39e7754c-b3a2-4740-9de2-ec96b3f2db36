<i18n>
{
  "en": {
    "checkin": "Check-in",
    "priceSecrecy": "Price Secrecy",
    "cancel": "Cancel",
    "checkinAndPrintRegistration": "Check-in and Print Registration Form",
    "roomType": "Room Type",
    "roomState": "Room Status",
    "preOrderedRoom": "Pre-booked Room",
    "roomsAssigned": "Rooms Assigned",
    "rooms": "Rooms",
    "maxRooms": "Maximum rooms exceeded! You can select up to {max} rooms.",
    "confirm": "Confirm"
  },
  "zh-cn": {
    "checkin": "办理入住",
    "priceSecrecy": "房价保密",
    "cancel": "取消",
    "checkinAndPrintRegistration": "入住并打印登记单",
    "roomType": "房型",
    "roomState": "房态",
    "preOrderedRoom": "预订单占用房间",
    "roomsAssigned": "已排房",
    "rooms": "间",
    "maxRooms": "最多选择 {max} 间房间",
    "confirm": "确定"
  },
  "km": {
    "checkin": "ចូលស្នាក់នៅ",
    "priceSecrecy": "ភាពសម្ងាត់តម្លៃ",
    "cancel": "បោះបង់",
    "checkinAndPrintRegistration": "ចូលស្នាក់នៅ និងបោះពុម្ពទម្រង់ចុះឈ្មោះ",
    "roomType": "ប្រភេទបន្ទប់",
    "roomState": "ស្ថានភាពបន្ទប់",
    "preOrderedRoom": "បន្ទប់ដែលបានកក់មុន",
    "roomsAssigned": "បន្ទប់ដែលបានចាត់តាំង",
    "rooms": "បន្ទប់",
    "maxRooms": "លើសពីចំនួនបន្ទប់អតិបរមា! អ្នកអាចជ្រើសរើសបានរហូតដល់ {max} បន្ទប់។",
    "confirm": "បញ្ជាក់"
  }
}
</i18n>

<script setup lang="ts">
import { bookApi, dictDataApi } from '@/api/modules/index'
import { BooleanEnum, DICT_TYPE_ROOM_STATUS, RoomState } from '@/models/index'
import useUserStore from '@/store/modules/user'
import dayjs from 'dayjs'

const props = withDefaults(
  defineProps<{
    roomNum?: number
  }>(),
  {
    roomNum: 0,
  }
)
const emits = defineEmits<{
  'update:modelValue': [value: boolean]
  success: [data: any]
}>()
const { t } = useI18n()
const myVisible = ref<boolean>(false)
const userStore = useUserStore()
const loading = ref(false)
const data = ref({
  rtCode: '',
  rtName: '',
  state: 'VC',
  isBookedRoom: BooleanEnum.NO.toString(),
  selectRooms: [] as string[],
  checkRoomList: [] as any[],
  dayPrices: [] as { date: string; price: string }[],
  planCheckinTime: '',
  planCheckoutTime: '',
  price: 0 as number | string,
  vipPrice: 0 as number | string,
})

const roomPrices = ref<{ rNo: string; rCode: string }[]>([])
const roomAll = ref<{ rNo: string; rCode: string; state: string; lockNo: string; mac: string; lockVersion: string; buildNo: string; floorNo: string }[]>([])
const rtsList = ref<
  {
    rtCode: string
    rtName: string
    price: string | number
    vipPrice: string | number
  }[]
>([])
const roomStates = ref<{ code: string; label: string }[]>([])

onMounted(async () => {})
const formData = ref<{ formData: any }>({ formData: {} })

async function open(obj: any) {
  data.value.checkRoomList = obj.rooms
  formData.value = obj
  if (obj.rooms.length > 0) {
    data.value.selectRooms = obj.rooms.map((v: { rNo: any }) => v.rNo)
  }
  myVisible.value = true
  await getRtsList()
  await getConstants()
  await getRooms()
}

async function getRtsList() {
  await bookApi
    .roomtypeList({
      ...formData.value.formData,
      delayMinute: 0,
      gcode: userStore.gcode,
      hcode: userStore.hcode,
    })
    .then((res: any) => {
      if (res.code !== 0) {
        ElMessage.error(`${t('roomType')} ${t('failed')}`)
        return
      }
      rtsList.value = res.data
      if (res.data && res.data.length > 0) {
        data.value.rtCode = res.data[0].rtCode
        data.value.rtName = res.data[0].rtName
        data.value.price = res.data[0].price
        data.value.vipPrice = res.data[0].dayPrices[0].vipPrice
        data.value.dayPrices = res.data[0].dayPrices
      }
    })
}

const dictTypes = [DICT_TYPE_ROOM_STATUS]

async function getConstants() {
  await dictDataApi.getDictDataBatch(dictTypes).then((res: any) => {
    roomStates.value = res.data.filter((item: any) => [RoomState.VC, RoomState.VD].includes(item.code))
  })
}

function changeState() {
  // 记录之前是否选择了预订单占用房间
  const wasBookedRoom = data.value.isBookedRoom === BooleanEnum.YES

  data.value.isBookedRoom = BooleanEnum.NO.toString()

  // 如果之前选择了预订单占用房间，需要重新调用接口获取房间数据
  if (wasBookedRoom) {
    getRooms(BooleanEnum.YES) // 传递之前的预订单占用房间状态
  } else {
    // 否则只是过滤现有数据
    roomPrices.value = roomAll.value.filter((item: { state: string }) => {
      return item.state === data.value.state
    })
  }
}

async function getRooms(forcePreOccupied?: string) {
  const preOccupiedValue = forcePreOccupied !== undefined ? forcePreOccupied : data.value.isBookedRoom
  const params = {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    rtCode: data.value.rtCode,
    state: data.value.state,
    planCheckinTime: dayjs(formData.value.formData.planCheckinTime).format('YYYY-MM-DD HH:mm'),
    planCheckoutTime: dayjs(formData.value.formData.planCheckoutTime).format('YYYY-MM-DD HH:mm'),
    isMeetingRoom: '0',
    preOccupied: preOccupiedValue,
  }
  loading.value = true
  await bookApi.canBookRoomList(params).then((res: any) => {
    loading.value = false
    if (res.code !== 0) {
      return ElMessage.error(res.msg)
    }
    roomAll.value = res.data
    // 当选中预订单占用房间时，显示所有返回的房间；否则按状态过滤
    if (preOccupiedValue === '1') {
      roomPrices.value = roomAll.value
    } else {
      roomPrices.value = res.data.filter((item: { state: string }) => {
        return item.state === data.value.state
      })
    }
  })
}

function rtChange() {
  if (rtsList.value && Array.isArray(rtsList.value) && data && typeof data.value === 'object') {
    data.value.rtName = rtsList.value.find((item) => item.rtCode === data.value.rtCode)?.rtName ?? ''
    data.value.price = rtsList.value.find((item) => item.rtCode === data.value.rtCode)?.price ?? ''
    data.value.vipPrice = rtsList.value.find((item) => item.rtCode === data.value.rtCode)?.dayPrices[0].vipPrice ?? ''
    data.value.dayPrices = rtsList.value.find((item) => item.rtCode === data.value.rtCode)?.dayPrices ?? []
  }
  getRooms()
}

function selectRoomType(rtCode: string) {
  // 如果点击的是已选中的房型，不执行任何操作
  if (data.value.rtCode === rtCode) {
    return
  }
  data.value.rtCode = rtCode
  rtChange()
}

function bookedRoomChange() {
  if (data.value.isBookedRoom === '1') {
    data.value.state = ''
  } else {
    data.value.state = RoomState.VC
  }
  getRooms()
}

function handleClose(tag: string) {
  data.value.selectRooms.splice(data.value.selectRooms.indexOf(tag), 1)
  data.value.checkRoomList.splice(
    data.value.checkRoomList.findIndex((v) => {
      return v.rNo === tag
    }),
    1
  )
}

function doCheck(val: any, _item: any) {
  if (props.roomNum > 0 && data.value.selectRooms.length > props.roomNum) {
    ElMessage.warning(t('maxRooms', { max: props.roomNum }))
    data.value.selectRooms = data.value.selectRooms.slice(0, props.roomNum)
    return false
  } else {
    if (!val) {
      data.value.checkRoomList.splice(
        data.value.checkRoomList.findIndex((v) => {
          return v.rNo === _item.rNo
        }),
        1
      )
    } else {
      data.value.checkRoomList.push({
        rNo: _item.rNo,
        rCode: _item.rCode,
        lockNo: _item.lockNo,
        mac: _item.mac,
        lockVersion: _item.lockVersion,
        buildNo: _item.buildNo,
        floorNo: _item.floorNo,
        rtCode: data.value.rtCode,
        rtName: data.value.rtName,
        price: data.value.price,
        vipPrice: data.value.vipPrice,
        dayPrices: data.value.dayPrices,
        idType: 'id_cert',
        list: [] as {
          guestName: string
          phone: string
          idType: string
          idNo: string
        }[],
        bkNum: 0,
      })
    }
    return true
  }
}

function onSubmit() {
  if (props.roomNum === 0 || data.value.selectRooms.length <= props.roomNum) {
    emits('success', data.value)
    myVisible.value = false
  }
}

function onClick() {
  myVisible.value = false
}

defineExpose({
  open,
})
</script>

<template>
  <div>
    <el-dialog v-model="myVisible" :title="t('roomsAssigned')" width="600px" :close-on-click-modal="false" append-to-body destroy-on-close @keydown.ctrl.enter.prevent="onSubmit">
      <el-form size="default" label-width="140px" inline-message inline class="search-form">
        <el-row>
          <el-col :span="24">
            <el-form-item :label="t('roomType')" label-width="80px">
              <div class="room-type-buttons">
                <el-button
                  v-for="item in rtsList"
                  :key="item.rtCode"
                  size="small"
                  :type="data.rtCode === item.rtCode ? 'primary' : 'default'"
                  @click="selectRoomType(item.rtCode)"
                >
                  {{ item.rtName }}
                </el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="t('roomState')" label-width="80px">
              <el-radio-group v-model="data.state" size="small" @change="changeState">
                <el-radio-button v-for="item in roomStates" :key="item.code" :value="item.code" border>
                  {{ item.label }}
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="t('preOrderedRoom')">
              <el-checkbox v-model="data.isBookedRoom" true-value="1" false-value="0" size="small" label="预订单占用房间" border @change="bookedRoomChange" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="special_td">
        <div style="margin-bottom: 15px">
          <span v-if="props.roomNum > 0">{{ t('roomsAssigned') }}: {{ data.selectRooms.length }} / {{ props.roomNum }} {{ t('rooms') }}</span>
          <span v-else>{{ t('roomsAssigned') }}: {{ data.selectRooms.length }}{{ t('rooms') }}</span>
        </div>
        <el-tag v-for="item in data.selectRooms" :key="item" class="roomtag" type="danger" closable @close="handleClose(item)">
          {{ item }}
        </el-tag>
      </div>
      <div v-loading="loading" class="roomList">
        <div class="flexBox">
          <ul style="padding-left: 0; margin-bottom: 0; list-style: none">
            <el-checkbox-group v-model="data.selectRooms">
              <li v-for="item in roomPrices" :key="item.rCode" class="xxx">
                {{ item }}
                <el-checkbox v-model="item.rNo" :value="item.rNo" border @change="(checked: any) => doCheck(checked, item)">
                  {{ item.rNo }}
                </el-checkbox>
              </li>
            </el-checkbox-group>
          </ul>
        </div>
      </div>
      <template #footer>
        <el-button size="large" @click="onClick">
          {{ t('cancel') }}
        </el-button>
        <el-button type="primary" size="large" @click="onSubmit">
          {{ t('confirm') }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.special_td {
  padding: 10px;
  line-height: 20px;
  color: #000;
  background: #f7f7f7;
  border-radius: 3px;
}

.xxx {
  position: relative;
  float: left;
  padding: 3px;
  margin-bottom: 5px;
  cursor: pointer;
}

.flexBox {
  display: flex;
  flex-flow: row wrap;
  align-items: center;
  justify-content: flex-start;
}

.roomList {
  height: 260px;
  padding: 5px;
  margin-top: 10px;
  overflow: auto;
  background-color: #f7f7f7;
}

.absolute-container {
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .page-header {
    margin-bottom: 0;
  }

  .page-main {
    display: flex;
    // 让 page-main 的高度自适应
    flex: 1;
    flex-direction: column;
    overflow: auto;

    .search-container {
      margin-bottom: 0;
    }
  }
}

.page-main {
  .search-form {
    flex-wrap: wrap;
    margin-bottom: -18px;

    :deep(.el-form-item) {
      flex: 1 1 300px;

      &:last-child {
        margin-left: auto;

        .el-form-item__content {
          justify-content: flex-end;
        }
      }
    }
  }
}

.el-form--inline .el-form-item {
  margin-bottom: 5px;
}

/* 表单标签和子元素统一字体大小 */
.search-form {
  :deep(.el-form-item__label) {
    font-size: 14px !important;
  }

  :deep(.el-radio-button__inner) {
    font-size: 14px !important;
    height: 30px !important;
    line-height: 30px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  :deep(.el-checkbox__label) {
    font-size: 14px !important;
  }

  :deep(.el-checkbox) {
    height: 30px !important;
    display: flex !important;
    align-items: center !important;
  }

  :deep(.el-checkbox__input) {
    display: flex !important;
    align-items: center !important;
  }
}

/* 房型按钮样式 */
.room-type-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 6px 8px;

  .el-button {
    margin: 0;
    font-size: 14px !important;
    height: 30px !important;

    &.el-button--primary {
      &:hover {
        background-color: var(--el-button-bg-color) !important;
        border-color: var(--el-button-border-color) !important;
        color: var(--el-button-text-color) !important;
        transform: none !important;
        box-shadow: none !important;
      }

      &:focus {
        background-color: var(--el-button-bg-color) !important;
        border-color: var(--el-button-border-color) !important;
        color: var(--el-button-text-color) !important;
        box-shadow: none !important;
      }

      &:active {
        background-color: var(--el-button-bg-color) !important;
        border-color: var(--el-button-border-color) !important;
        color: var(--el-button-text-color) !important;
        transform: none !important;
      }

      &:focus-visible {
        background-color: var(--el-button-bg-color) !important;
        border-color: var(--el-button-border-color) !important;
        color: var(--el-button-text-color) !important;
        box-shadow: none !important;
      }
    }
  }
}

.roomtag {
  margin-right: 5px;
  margin-bottom: 8px;
  font-size: 14px !important;
  height: 32px !important;
  line-height: 30px !important;
  display: inline-flex !important;
  align-items: center !important;
}
</style>
