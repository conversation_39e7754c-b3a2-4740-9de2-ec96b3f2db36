<i18n>
{
  "en": {
    "roomArrangement": "Room Arrangement",
    "roomType": "Room Type",
    "roomStatus": "Room Status",
    "others": "Others",
    "bookedRoom": "Booked Room",
    "roomsArranged": "Rooms Arranged",
    "rooms": "rooms",
    "cancel": "Cancel",
    "confirm": "Confirm",
    "roomArrangementSuccess": "Room arrangement successful",
    "maxRoomSelection": "You can select up to {num} rooms",
    "getRoomTypeListError": "Failed to retrieve room type list"
  },
  "zh-cn": {
    "roomArrangement": "排房",
    "roomType": "房型",
    "roomStatus": "房态",
    "others": "其他",
    "bookedRoom": "预订单占用房间",
    "roomsArranged": "已排房",
    "rooms": "间",
    "cancel": "取消",
    "confirm": "确定",
    "roomArrangementSuccess": "排房成功",
    "maxRoomSelection": "最多选择{num}间房间",
    "getRoomTypeListError": "获取房型列表失败"
  },
  "km": {
    "roomArrangement": "ការរៀបចំបន្ទប់",
    "roomType": "ប្រភេទបន្ទប់",
    "roomStatus": "ស្ថានភាពបន្ទប់",
    "others": "ផ្សេងៗ",
    "bookedRoom": "បន្ទប់ដែលបានកក់",
    "roomsArranged": "បន្ទប់ដែលបានរៀបចំ",
    "rooms": "បន្ទប់",
    "cancel": "បោះបង់",
    "confirm": "បញ្ជាក់",
    "roomArrangementSuccess": "ការរៀបចំបន្ទប់ជោគជ័យ",
    "maxRoomSelection": "អ្នកអាចជ្រើសរើសបានរហូតដល់ {num} បន្ទប់",
    "getRoomTypeListError": "មិនអាចទាញយកបញ្ជីប្រភេទបន្ទប់បាន"
  }
}
</i18n>

<script setup lang="ts">
import type { DetailFormProps } from './types'
import { bookApi, dictDataApi } from '@/api/modules/index'
import { BooleanEnum, DICT_TYPE_ROOM_STATUS, RoomState } from '@/models'
import useUserStore from '@/store/modules/user'
import dayjs from 'dayjs'

const props = withDefaults(
  defineProps<
    {
      modelValue?: boolean
      rooms?: Array<any>
      isAlone?: boolean
    } & DetailFormProps
  >(),
  {
    rtCode: '',
    rtName: '',
    checkinType: '',
    guestSrcType: '',
    orderSource: '',
    roomNum: 0,
    rtState: '',
    rNos: (): string[] => {
      // 返回一个字符串数组
      return []
    },
    planCheckinTime: '',
    planCheckoutTime: '',
    rooms: [],
    bookNo: '',
    orderNo: '',
    isAlone: false,
    modelValue: false,
  }
)
const emits = defineEmits<{
  'update:modelValue': [value: boolean]
  success: [data: any]
  reload: [data: any]
}>()
const { t } = useI18n()
const userStore = useUserStore()
const loading = ref(false)
const data = ref({
  rtCode: props.rtCode,
  rtName: props.rtName,
  /** 房间状态 VC 空净  VD 空脏  OC 住净 OD 住脏  OO维修 */
  state: props.rtState ? props.rtState : 'VC',
  /** 预订单占用房间 0:否 1:是 */
  isBookedRoom: '0',
  selectRooms: [] as string[],
  dayPrices: [] as { date: string; price: string }[],
  planCheckinTime: props.planCheckinTime,
  planCheckoutTime: props.planCheckoutTime,
  // 定价
  price: 0 as number | string,
  bookRooms: [] as {
    preOccupied: string
    state: string
    rNo: string
    rCode: string
  }[],
})

/** 房间列表 */
const roomPrices = ref<{ rNo: string; rCode: string }[]>([])
const roomAll = ref<{ rNo: string; rCode: string; state: string; lockNo: string }[]>([])
/** 房型列表 */
const rts = ref<{ rtCode: string; rtName: string; price: string | number }[]>([])
const roomList = ref<{ rNo: string; rCode: string; rtCode: string; rtName: string }[]>([])
onMounted(async () => {
  getConstants()
  if (props.rNos.length > 0) {
    data.value.selectRooms = props.rNos
  }
  await getRooms()
  roomList.value = props.rooms && props.rooms.length > 0 ? props.rooms : []
  if (props.rtCode === '') {
    await getRts()
  }
})

async function getRts() {
  await bookApi
    .roomtypeList({
      gcode: userStore.gcode,
      hcode: userStore.hcode,
      channelCode: 'lobby', // 渠道  默认门店 lobby
      delayMinute: 0,
      checkinType: props.checkinType, // 入住类型
      guestSrcType: props.guestSrcType, // 客源
      orderSource: props.orderSource, // 订单来源
      planCheckinTime: props.planCheckinTime,
      planCheckoutTime: props.planCheckoutTime,
    })
    .then((res: any) => {
      if (res.code !== 0) {
        ElMessage.error(t('getRoomTypeListError'))
        return
      }
      rts.value = res.data
      if (rts.value.length > 0) {
        data.value.rtCode = rts.value[0].rtCode
        data.value.rtName = rts.value[0].rtName
        data.value.price = rts.value[0].price
      }
    })
}

// 通用字典
const dictTypes = [DICT_TYPE_ROOM_STATUS]
/** 房间状态 */
const roomStates = ref<{ code: string; label: string }[]>([])

function getConstants() {
  dictDataApi.getDictDataBatch(dictTypes).then((res: any) => {
    roomStates.value = res.data.filter((item: any) => item.code !== RoomState.OO)
  })
}

async function getRooms(forcePreOccupied?: string) {
  loading.value = true
  const preOccupiedValue = forcePreOccupied !== undefined ? forcePreOccupied : data.value.isBookedRoom
  const params = {
    gcode: userStore.gcode,
    hcode: userStore.hcode,
    rtCode: data.value.rtCode,
    state: data.value.state,
    planCheckinTime: dayjs(props.planCheckinTime).format('YYYY-MM-DD HH:mm'),
    planCheckoutTime: dayjs(props.planCheckoutTime).format('YYYY-MM-DD HH:mm'),
    isMeetingRoom: '0',
    preOccupied: preOccupiedValue,
  }
  await bookApi.canBookRoomList(params).then((res: any) => {
    loading.value = false
    if (res.code !== 0) {
      ElMessage.error(res.msg)
      return
    }
    roomAll.value = res.data
    // 当选中预订单占用房间时，显示所有返回的房间；否则按状态过滤
    if (preOccupiedValue === BooleanEnum.YES) {
      roomPrices.value = roomAll.value
    } else {
      roomPrices.value = roomAll.value.filter((item: { state: string }) => {
        return item.state === data.value.state
      })
    }
  })
}

function handleClose(tag: string) {
  data.value.selectRooms.splice(data.value.selectRooms.indexOf(tag), 1)
}

function doCheck(val: any) {
  if (props.roomNum > 0 && data.value.selectRooms.length > props.roomNum) {
    ElMessage.warning(t('maxRoomSelection', { num: props.roomNum }))
    data.value.selectRooms = data.value.selectRooms.slice(0, props.roomNum)
    return false
  } else {
    if (roomList.value.length < data.value.selectRooms.length) {
      roomList.value.unshift({
        rNo: val.rNo,
        rCode: val.rCode,
        rtCode: data.value.rtCode,
        rtName: data.value.rtName,
      })
    } else {
      // roomList.value = roomList.value.slice(0, 1)
      roomList.value = roomList.value.filter((item) => {
        return data.value.selectRooms.includes(item.rNo)
      })
    }
    return true
  }
}
function changeState() {
  // 记录之前是否选择了预订单占用房间
  const wasBookedRoom = data.value.isBookedRoom === '1'

  data.value.isBookedRoom = '0'

  // 如果之前选择了预订单占用房间，需要重新调用接口获取房间数据
  if (wasBookedRoom) {
    getRooms('0') // 传递当前的预订单占用房间状态（已重置为0）
  } else {
    // 否则只是过滤现有数据
    roomPrices.value = roomAll.value.filter((item: { state: string }) => {
      return item.state === data.value.state
    })
  }
}

function rtChange() {
  if (rts.value && Array.isArray(rts.value) && data && typeof data.value === 'object') {
    data.value.rtName = rts.value.find((item) => item.rtCode === data.value.rtCode)?.rtName ?? ''
    data.value.price = rts.value.find((item) => item.price === data.value.price)?.price ?? ''
  } else {
    console.error('rts or data is not in the expected format.')
  }
  getRooms()
}

function selectRoomType(rtCode: string) {
  // 如果点击的是已选中的房型，不执行任何操作
  if (data.value.rtCode === rtCode) {
    return
  }
  data.value.rtCode = rtCode
  rtChange()
}

function bookedRoomChange() {
  if (data.value.isBookedRoom === BooleanEnum.YES) {
    data.value.state = '' as RoomState
  } else {
    data.value.state = RoomState.VC
  }
  getRooms()
}
const myVisible = computed({
  get() {
    return props.modelValue
  },
  set(val: boolean) {
    emits('update:modelValue', val)
  },
})
function onSubmit() {
  if (data.value.selectRooms.length <= props.roomNum) {
    const list = roomList.value.filter((person) => data.value.selectRooms.includes(person.rNo))
    // list = roomPrices.value.filter((person) =>
    //   data.value.selectRooms.includes(person.rNo)
    // );
    data.value.bookRooms = list.map((item) => {
      return {
        ...item,
        preOccupied: data.value.isBookedRoom,
        state: data.value.state,
      }
    })
    if (props.isAlone) {
      // 单独排房
      const params = {
        gcode: userStore.gcode,
        hcode: userStore.hcode,
        bookNo: props.bookNo,
        batchNo: props.batchNo,
        rooms: [
          {
            rCode: data.value.bookRooms[0]?.rCode,
            rNo: data.value.bookRooms[0]?.rNo,
            orderNo: props.orderNo,
          },
        ],
      }
      bookApi.arrangeBook(params).then((res: any) => {
        if (res.code === 0) {
          ElMessage.success(t('roomArrangementSuccess'))
          emits('reload', data.value.selectRooms)
          onCancel()
        }
      })
    } else {
      emits('success', data.value)
      onCancel()
    }
  }
}
function onCancel() {
  myVisible.value = false
}
</script>

<template>
  <div>
    <el-dialog v-model="myVisible" :title="t('roomArrangement')" width="600px" :close-on-click-modal="false" append-to-body destroy-on-close @keydown.ctrl.enter.prevent="onSubmit">
      <el-form size="default" label-width="120px" inline-message inline class="search-form">
        <el-row>
          <el-col :span="24">
            <el-form-item :label="t('roomType')" label-width="80px">
              <div class="room-type-buttons">
                <el-button v-if="props.rtCode !== ''" type="primary" size="small">
                  {{ props.rtName }}
                </el-button>
                <el-button v-for="item in rts" v-else :key="item.rtCode" size="small" :type="data.rtCode === item.rtCode ? 'primary' : 'default'" @click="selectRoomType(item.rtCode)">
                  {{ item.rtName }}
                </el-button>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="t('roomStatus')" label-width="80px">
              <el-radio-group v-model="data.state" size="small" @change="changeState">
                <el-radio-button v-for="item in roomStates" :key="item.code" :value="item.code" border>
                  {{ item.label }}
                </el-radio-button>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item :label="t('others')" label-width="80px">
              <el-checkbox v-model="data.isBookedRoom" true-value="1" false-value="0" size="small" :label="t('bookedRoom')" border @change="bookedRoomChange()" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div class="special_td">
        <div style="margin-bottom: 15px">
          <span v-if="props.roomNum > 0">{{ t('roomsArranged') }}: {{ data.selectRooms.length }} / {{ props.roomNum }} {{ t('rooms') }}</span>
          <span v-else>{{ t('roomsArranged') }}: {{ data.selectRooms.length }}{{ t('rooms') }}</span>
        </div>
        <el-tag v-for="item in data.selectRooms" :key="item" class="roomtag" type="danger" closable @close="handleClose(item)">
          {{ item }}
        </el-tag>
      </div>
      <div class="roomList">
        <div class="flexBox">
          <ul style="padding-left: 0; margin-bottom: 0; list-style: none">
            <el-checkbox-group v-model="data.selectRooms">
              <li v-for="item in roomPrices" :key="item.rCode" class="xxx">
                {{ item }}
                <el-checkbox v-model="item.rNo" :value="item.rNo" border @change="doCheck(item)">
                  {{ item.rNo }}
                </el-checkbox>
              </li>
            </el-checkbox-group>
          </ul>
        </div>
      </div>
      <template #footer>
        <el-button size="large" @click="onCancel">
          {{ t('cancel') }}
        </el-button>
        <el-button type="primary" size="large" @click="onSubmit">
          {{ t('confirm') }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.special_td {
  padding: 10px;
  line-height: 20px;
  color: #000;
  background: #f7f7f7;
  border-radius: 3px;
}

.xxx {
  position: relative;
  float: left;
  padding: 3px;
  margin-bottom: 5px;
  cursor: pointer;
}

.flexBox {
  display: flex;
  flex-flow: row wrap;
  align-items: center;
  justify-content: flex-start;
}

.roomList {
  height: 260px;
  padding: 5px;
  margin-top: 10px;
  overflow: auto;
  background-color: #f7f7f7;
}

.absolute-container {
  position: absolute;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;

  .page-header {
    margin-bottom: 0;
  }

  .page-main {
    display: flex;
    // 让 page-main 的高度自适应
    flex: 1;
    flex-direction: column;
    overflow: auto;

    .search-container {
      margin-bottom: 0;
    }
  }
}

.page-main {
  .search-form {
    flex-wrap: wrap;
    margin-bottom: -18px;

    :deep(.el-form-item) {
      flex: 1 1 300px;

      &:last-child {
        margin-left: auto;

        .el-form-item__content {
          justify-content: flex-end;
        }
      }
    }
  }
}

.el-form--inline .el-form-item {
  margin-bottom: 5px;
}

/* 表单标签和子元素统一字体大小 */
.search-form {
  :deep(.el-form-item__label) {
    font-size: 14px !important;
  }

  :deep(.el-radio-button__inner) {
    font-size: 14px !important;
    height: 30px !important;
    line-height: 30px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  :deep(.el-checkbox__label) {
    font-size: 14px !important;
  }

  :deep(.el-checkbox) {
    height: 30px !important;
    display: flex !important;
    align-items: center !important;
  }

  :deep(.el-checkbox__input) {
    display: flex !important;
    align-items: center !important;
  }
}

/* 房型按钮样式 */
.room-type-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px 4px;

  .el-button {
    margin: 0;
    font-size: 14px !important;
    height: 30px !important;

    &.el-button--primary {
      &:hover {
        background-color: var(--el-button-bg-color) !important;
        border-color: var(--el-button-border-color) !important;
        color: var(--el-button-text-color) !important;
        transform: none !important;
        box-shadow: none !important;
      }

      &:focus {
        background-color: var(--el-button-bg-color) !important;
        border-color: var(--el-button-border-color) !important;
        color: var(--el-button-text-color) !important;
        box-shadow: none !important;
      }

      &:active {
        background-color: var(--el-button-bg-color) !important;
        border-color: var(--el-button-border-color) !important;
        color: var(--el-button-text-color) !important;
        transform: none !important;
      }

      &:focus-visible {
        background-color: var(--el-button-bg-color) !important;
        border-color: var(--el-button-border-color) !important;
        color: var(--el-button-text-color) !important;
        box-shadow: none !important;
      }
    }
  }
}

/* 其他文字元素统一字体大小 */
.special_td {
  font-size: 14px !important;
}

.roomtag {
  margin-right: 5px;
  margin-bottom: 8px;
  font-size: 14px !important;
  height: 30px !important;
  line-height: 30px !important;
  display: inline-flex !important;
  align-items: center !important;
}

.roomList {
  font-size: 14px !important;

  .el-checkbox {
    font-size: 14px !important;
  }

  .el-checkbox__label {
    font-size: 14px !important;
  }
}
</style>
