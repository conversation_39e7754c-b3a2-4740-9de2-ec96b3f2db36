<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" id="app-favicon" href="./favicon.ico" />
    <link rel="stylesheet" href="/browser_upgrade/index.css" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta http-equiv="Expires" content="0" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Cache-control" content="no-cache" />
    <meta http-equiv="Cache" content="no-cache" />
    <title id="app-title">加载中...</title>
    <script>
      // 动态设置页面标题和favicon
      ;(function () {
        try {
          const envTitle = '%VITE_APP_TITLE%'
          const envFavicon = '%VITE_APP_FAVICON%'

          // 清理可能的换行符和多余空格
          const cleanedEnvTitle = envTitle.replace(/\s+/g, ' ').trim()
          const cleanedEnvFavicon = envFavicon.replace(/\s+/g, ' ').trim()

          const titleConfig = JSON.parse(cleanedEnvTitle)
          const faviconConfig = JSON.parse(cleanedEnvFavicon)
          const hostname = window.location.hostname

          // 根据域名获取对应的标题
          const title = titleConfig[hostname] || titleConfig.default || '酒店智能体'
          document.getElementById('app-title').textContent = title
          document.title = title

          // 根据域名获取对应的favicon
          const faviconUrl = faviconConfig[hostname] || faviconConfig.default || './favicon.ico'
          document.getElementById('app-favicon').href = faviconUrl
        } catch (error) {
          console.error('设置动态标题和favicon失败:', error)
          console.error('错误详情:', error.message)
          // 如果解析失败，使用默认值
          document.getElementById('app-title').textContent = '酒店智能体'
          document.title = '酒店智能体'
          document.getElementById('app-favicon').href = './favicon.ico'
        }
      })()
    </script>
  </head>

  <body>
    <div id="app">
      <div id="browser-upgrade">
        <div class="title">为了您的体验，推荐使用以下浏览器</div>
        <div class="browsers">
          <a href="https://www.google.cn/chrome/" target="_blank" class="browser">
            <img class="browser-icon" src="/browser_upgrade/chrome.png" />
            <div class="browser-name">Google Chrome</div>
          </a>
        </div>
      </div>
    </div>
    <script>
      if (!!window.ActiveXObject || 'ActiveXObject' in window) {
        document.getElementById('browser-upgrade').style.display = 'block'
      }
    </script>
    <script src="/qwebchannel.js"></script>
    <script type="module" src="/src/main.ts"></script>

    <!-- 其他代码 -->
    <script src="https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/stimulsoft-reports-js/stimulsoft.reports.js"></script>
    <script src="https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/stimulsoft-reports-js/stimulsoft.viewer.js"></script>
    <script src="https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/stimulsoft-reports-js/stimulsoft.designer.js"></script>
    <script src="https://aflower-1303913307.cos.ap-guangzhou.myqcloud.com/stimulsoft-reports-js/stimulsoft.blockly.editor.js"></script>
    <script src="https://lf-cdn.coze.cn/obj/unpkg/flow-platform/chat-app-sdk/1.2.0-beta.10/libs/cn/index.js"></script>
  </body>
</html>
